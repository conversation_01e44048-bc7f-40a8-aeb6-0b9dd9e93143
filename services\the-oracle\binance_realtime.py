#!/usr/bin/env python3
"""
Production Binance Real-time Monitor
Stable WebSocket connection for unlock detection
"""

import json
import time
import logging
import threading
import websocket
from datetime import datetime, timedelta
from typing import Dict, List, Callable, Optional

class BinanceRealTimeMonitor:
    """
    Production-ready Binance WebSocket monitor for unlock detection.
    Handles connection stability, reconnection, and unlock alerts.
    """
    
    def __init__(self):
        self.ws = None
        self.connected = False
        self.running = False
        self.message_count = 0
        self.price_data = {}
        self.callbacks = []
        
        # Unlock detection settings
        self.PRICE_DROP_THRESHOLD = -15.0  # 15% drop triggers alert
        self.VOLUME_SPIKE_THRESHOLD = 2.0   # 2x normal volume
        
        # Connection settings
        self.RECONNECT_DELAY = 5
        self.MAX_RECONNECT_ATTEMPTS = 10
        self.reconnect_attempts = 0
        
        # Symbols to monitor (DeFi tokens most likely to have unlocks)
        self.symbols = [
            'UNIUSDT',   # Uniswap
            'AAVEUSDT',  # Aave
            'COMPUSDT',  # Compound
            'LINKUSDT',  # Chainlink
            'MKRUSDT',   # MakerDAO
        ]
        
    def add_callback(self, callback: Callable):
        """Add callback for price updates and unlock alerts"""
        self.callbacks.append(callback)
        
    def start_monitoring(self):
        """Start real-time monitoring"""
        if self.running:
            logging.warning("Monitor already running")
            return
            
        self.running = True
        self.reconnect_attempts = 0
        
        logging.info(f"🚀 Starting Binance real-time monitor for {len(self.symbols)} symbols")
        self._connect()
        
    def stop_monitoring(self):
        """Stop monitoring"""
        self.running = False
        self.connected = False
        
        if self.ws:
            self.ws.close()
            
        logging.info("⏹️ Binance monitoring stopped")
        
    def _connect(self):
        """Establish WebSocket connection"""
        if not self.running:
            return
            
        try:
            # Use simple stream endpoint for stability
            ws_url = "wss://stream.binance.com:9443/ws"
            
            logging.info(f"🔗 Connecting to Binance WebSocket...")
            
            self.ws = websocket.WebSocketApp(
                ws_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open
            )
            
            # Start in background thread
            ws_thread = threading.Thread(target=self._run_websocket)
            ws_thread.daemon = True
            ws_thread.start()
            
        except Exception as e:
            logging.error(f"❌ Failed to connect: {e}")
            self._schedule_reconnect()
            
    def _run_websocket(self):
        """Run WebSocket with error handling"""
        try:
            self.ws.run_forever(
                ping_interval=30,  # Keep connection alive
                ping_timeout=10
            )
        except Exception as e:
            logging.error(f"❌ WebSocket error: {e}")
            if self.running:
                self._schedule_reconnect()
                
    def _on_open(self, ws):
        """Connection opened - subscribe to streams"""
        self.connected = True
        self.reconnect_attempts = 0
        
        logging.info("✅ Connected to Binance WebSocket")
        
        # Subscribe to ticker streams
        for i, symbol in enumerate(self.symbols):
            try:
                subscribe_msg = {
                    "method": "SUBSCRIBE",
                    "params": [f"{symbol.lower()}@ticker"],
                    "id": i + 1
                }
                
                ws.send(json.dumps(subscribe_msg))
                logging.info(f"📡 Subscribed to {symbol}")
                time.sleep(0.2)  # Avoid rate limits
                
            except Exception as e:
                logging.error(f"❌ Failed to subscribe to {symbol}: {e}")
                
    def _on_message(self, ws, message):
        """Process incoming price data"""
        try:
            data = json.loads(message)
            self.message_count += 1
            
            # Handle subscription confirmations
            if 'result' in data:
                if data['result'] is None:
                    logging.debug(f"✅ Subscription confirmed: {data.get('id')}")
                return
                
            # Handle ticker data
            if 'stream' in data and '@ticker' in data['stream']:
                self._process_ticker(data['data'])
                
        except Exception as e:
            logging.error(f"❌ Message processing error: {e}")
            
    def _process_ticker(self, ticker_data):
        """Process ticker data and check for unlock indicators"""
        try:
            symbol = ticker_data['s']
            price = float(ticker_data['c'])
            change_24h = float(ticker_data['P'])
            volume = float(ticker_data['v'])
            
            # Store current data
            self.price_data[symbol] = {
                'price': price,
                'change_24h': change_24h,
                'volume': volume,
                'timestamp': datetime.now()
            }
            
            # Check for unlock indicators
            unlock_detected = False
            indicators = []
            
            # Significant price drop
            if change_24h <= self.PRICE_DROP_THRESHOLD:
                unlock_detected = True
                indicators.append(f"Price drop: {change_24h:.2f}%")
                
            # Additional checks could include volume analysis, etc.
            
            if unlock_detected:
                self._alert_unlock_detected(symbol, indicators, ticker_data)
                
            # Notify callbacks
            for callback in self.callbacks:
                try:
                    callback('ticker', symbol, {
                        'price': price,
                        'change_24h': change_24h,
                        'volume': volume,
                        'unlock_detected': unlock_detected,
                        'indicators': indicators
                    })
                except Exception as e:
                    logging.error(f"❌ Callback error: {e}")
                    
        except Exception as e:
            logging.error(f"❌ Ticker processing error: {e}")
            
    def _alert_unlock_detected(self, symbol, indicators, ticker_data):
        """Alert about potential unlock event"""
        price = float(ticker_data['c'])
        change = float(ticker_data['P'])
        
        alert_msg = f"🚨 UNLOCK ALERT: {symbol}\n"
        alert_msg += f"   Price: ${price:.4f} ({change:+.2f}%)\n"
        alert_msg += f"   Indicators: {', '.join(indicators)}\n"
        alert_msg += f"   Time: {datetime.now().strftime('%H:%M:%S')}"
        
        logging.warning(alert_msg)
        
        # Notify callbacks about unlock
        for callback in self.callbacks:
            try:
                callback('unlock_alert', symbol, {
                    'symbol': symbol,
                    'price': price,
                    'change_24h': change,
                    'indicators': indicators,
                    'timestamp': datetime.now(),
                    'confidence': 'high' if change <= -20 else 'medium'
                })
            except Exception as e:
                logging.error(f"❌ Unlock callback error: {e}")
                
    def _on_error(self, ws, error):
        """Handle WebSocket errors"""
        logging.error(f"❌ WebSocket error: {error}")
        
    def _on_close(self, ws, close_status_code, close_msg):
        """Handle connection close"""
        self.connected = False
        
        if close_status_code == 1000:  # Normal close
            logging.info("ℹ️ WebSocket closed normally")
        else:
            logging.warning(f"❌ WebSocket closed unexpectedly: {close_status_code}")
            
        if self.running:
            self._schedule_reconnect()
            
    def _schedule_reconnect(self):
        """Schedule reconnection attempt"""
        if not self.running or self.reconnect_attempts >= self.MAX_RECONNECT_ATTEMPTS:
            return
            
        self.reconnect_attempts += 1
        delay = self.RECONNECT_DELAY * self.reconnect_attempts
        
        logging.info(f"🔄 Reconnecting in {delay}s (attempt {self.reconnect_attempts}/{self.MAX_RECONNECT_ATTEMPTS})")
        
        def reconnect():
            time.sleep(delay)
            if self.running:
                self._connect()
                
        reconnect_thread = threading.Thread(target=reconnect)
        reconnect_thread.daemon = True
        reconnect_thread.start()
        
    def get_status(self) -> Dict:
        """Get current monitor status"""
        return {
            'connected': self.connected,
            'running': self.running,
            'message_count': self.message_count,
            'symbols_monitored': len(self.symbols),
            'reconnect_attempts': self.reconnect_attempts,
            'price_data_count': len(self.price_data)
        }
        
    def get_current_prices(self) -> Dict:
        """Get current price data"""
        return self.price_data.copy()

# Global instance
binance_monitor = BinanceRealTimeMonitor()

def start_binance_monitoring(callback=None):
    """Start Binance monitoring with optional callback"""
    if callback:
        binance_monitor.add_callback(callback)
    binance_monitor.start_monitoring()
    return binance_monitor

if __name__ == "__main__":
    # Test the monitor
    logging.basicConfig(level=logging.INFO)
    
    def test_callback(event_type, symbol, data):
        if event_type == 'unlock_alert':
            print(f"🚨 {symbol}: {data['indicators']}")
        elif event_type == 'ticker':
            price = data['price']
            change = data['change_24h']
            print(f"📊 {symbol}: ${price:.4f} ({change:+.2f}%)")
    
    # Start monitoring
    monitor = start_binance_monitoring(test_callback)
    
    try:
        print("🚀 Monitoring started. Press Ctrl+C to stop...")
        while True:
            time.sleep(10)
            status = monitor.get_status()
            print(f"📈 Status: Connected={status['connected']}, Messages={status['message_count']}")
    except KeyboardInterrupt:
        print("\n⏹️ Stopping monitor...")
        monitor.stop_monitoring()
        print("✅ Monitor stopped")
