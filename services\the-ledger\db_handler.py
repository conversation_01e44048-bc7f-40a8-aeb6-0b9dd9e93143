import os
import psycopg2
import psycopg2.extras
from typing import Dict, Any, List
from datetime import datetime, timedelta
import logging

# Database connection details from <PERSON>der's environment
DB_URL = os.environ.get("DATABASE_URL")

def _get_db_conn():
    """Get database connection"""
    if not DB_URL:
        raise Exception("DATABASE_URL environment variable not set")
    return psycopg2.connect(DB_URL)

def get_open_positions() -> List[Dict[str, Any]]:
    """Retrieves all open trading positions"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """)
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying open positions: {e}")
        return []
    finally:
        conn.close()

def update_position_status(position_id: int, status: str, close_tx_hash: str = None):
    """Update position status"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            if status == 'CLOSED':
                cur.execute("""
                    UPDATE positions 
                    SET status = %s, close_tx_hash = %s, closed_at = NOW()
                    WHERE id = %s
                """, (status, close_tx_hash, position_id))
            else:
                cur.execute("""
                    UPDATE positions 
                    SET status = %s
                    WHERE id = %s
                """, (status, position_id))
            
            conn.commit()
            logging.info(f"Updated position {position_id} status to {status}")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error updating position status: {e}")
        raise
    finally:
        conn.close()

def get_position_by_id(position_id: int) -> Dict[str, Any]:
    """Get a specific position by ID"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, close_tx_hash,
                       created_at, closed_at
                FROM positions 
                WHERE id = %s
            """, (position_id,))
            
            result = cur.fetchone()
            return dict(result) if result else {}
    except Exception as e:
        logging.error(f"Error querying position {position_id}: {e}")
        return {}
    finally:
        conn.close()

def get_positions_summary() -> Dict[str, Any]:
    """Get summary statistics of all positions"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            # Get counts by status
            cur.execute("""
                SELECT status, COUNT(*) as count
                FROM positions 
                GROUP BY status
            """)
            status_counts = dict(cur.fetchall())
            
            # Get total amounts
            cur.execute("""
                SELECT 
                    COUNT(*) as total_positions,
                    SUM(CASE WHEN status = 'OPEN' THEN amount_shorted * entry_price_in_usdc ELSE 0 END) as open_value_usd,
                    AVG(CASE WHEN status = 'OPEN' THEN entry_price_in_usdc ELSE NULL END) as avg_entry_price
                FROM positions
            """)
            totals = cur.fetchone()
            
            return {
                "status_counts": status_counts,
                "total_positions": totals[0] or 0,
                "open_value_usd": float(totals[1] or 0),
                "avg_entry_price": float(totals[2] or 0)
            }
    except Exception as e:
        logging.error(f"Error getting positions summary: {e}")
        return {}
    finally:
        conn.close()

def get_positions_by_token(token_address: str) -> List[Dict[str, Any]]:
    """Get all positions for a specific token"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, close_tx_hash,
                       created_at, closed_at
                FROM positions 
                WHERE token_address = %s
                ORDER BY created_at DESC
            """, (token_address,))
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying positions for token {token_address}: {e}")
        return []
    finally:
        conn.close()

def get_positions_near_unlock(days_ahead: int = 7) -> List[Dict[str, Any]]:
    """Get positions with unlock dates approaching"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            end_date = datetime.now() + timedelta(days=days_ahead)
            
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions 
                WHERE status = 'OPEN' 
                AND unlock_date BETWEEN NOW() AND %s
                ORDER BY unlock_date ASC
            """, (end_date,))
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying positions near unlock: {e}")
        return []
    finally:
        conn.close()
