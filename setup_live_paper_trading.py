#!/usr/bin/env python3
"""
Setup script for Live Paper Trading
Checks configuration and prepares environment for live market testing
"""

import os
import sys
from pathlib import Path

def check_python_dependencies():
    """Check if required Python packages are installed"""
    print("🐍 Checking Python Dependencies...")
    
    required_packages = [
        'requests',
        'redis', 
        'psycopg2',
        'web3',
        'eth_account'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"   ✅ {package}")
        except ImportError:
            print(f"   ❌ {package} - MISSING")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️ Missing packages: {', '.join(missing_packages)}")
        print("📦 Install with: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ All Python dependencies satisfied")
    return True

def setup_environment_variables():
    """Setup environment variables for paper trading"""
    print("\n🔧 Setting up Environment Variables...")
    
    # Core paper trading settings
    env_vars = {
        'PAPER_TRADING_MODE': 'true',
        'PRESSURE_SCORE_THRESHOLD': '0.75',
        'STOP_LOSS_PCT': '0.15',
        'TAKE_PROFIT_PCT': '0.10',
        'BORROW_AMOUNT_PER_TRADE': '1000',
        'MONITORING_INTERVAL_SECONDS': '60',
        'TAKE_PROFIT_DAYS_BEFORE_UNLOCK': '1'
    }
    
    for key, default_value in env_vars.items():
        current_value = os.environ.get(key)
        if not current_value:
            os.environ[key] = default_value
            print(f"   🔧 Set {key} = {default_value}")
        else:
            print(f"   ✅ {key} = {current_value}")
    
    # Optional API keys (for enhanced data sources)
    optional_keys = [
        'COINGECKO_API_KEY',
        'DEXSCREENER_API_KEY', 
        'ETHERSCAN_API_KEY',
        'TELEGRAM_BOT_TOKEN',
        'TELEGRAM_CHAT_ID'
    ]
    
    print("\n📡 Optional API Keys (for enhanced features):")
    for key in optional_keys:
        value = os.environ.get(key)
        if value:
            print(f"   ✅ {key} = {'*' * (len(value) - 4) + value[-4:]}")
        else:
            print(f"   ⚪ {key} = Not set (using free tier/mock data)")
    
    return True

def check_service_files():
    """Check if all service files are present"""
    print("\n📁 Checking Service Files...")
    
    required_services = {
        'the-oracle': ['main.py', 'data_sources.py', 'event_publisher.py'],
        'the-seer': ['main.py', 'analysis.py', 'onchain_checker.py'],
        'the-executioner': ['main.py', 'paper_trading.py', 'wallet_manager.py'],
        'the-ledger': ['main.py', 'risk_manager.py', 'price_fetcher.py'],
        'the-herald': ['main.py', 'telegram_bot.py']
    }
    
    all_files_present = True
    
    for service, files in required_services.items():
        service_path = Path('services') / service
        print(f"   📂 {service}:")
        
        for file in files:
            file_path = service_path / file
            if file_path.exists():
                print(f"      ✅ {file}")
            else:
                print(f"      ❌ {file} - MISSING")
                all_files_present = False
    
    if all_files_present:
        print("✅ All service files present")
    else:
        print("❌ Some service files are missing")
    
    return all_files_present

def test_market_data_connection():
    """Test connection to market data sources"""
    print("\n🌐 Testing Market Data Connections...")
    
    try:
        import requests
        
        # Test CoinGecko (free tier)
        print("   📊 Testing CoinGecko API...")
        response = requests.get("https://api.coingecko.com/api/v3/ping", timeout=10)
        if response.status_code == 200:
            print("      ✅ CoinGecko API accessible")
        else:
            print(f"      ⚠️ CoinGecko API returned status {response.status_code}")
        
        # Test a sample token price fetch
        print("   💰 Testing token price fetch...")
        eth_price_url = "https://api.coingecko.com/api/v3/simple/price?ids=ethereum&vs_currencies=usd"
        response = requests.get(eth_price_url, timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            eth_price = data.get('ethereum', {}).get('usd', 'Unknown')
            print(f"      ✅ ETH Price: ${eth_price}")
        else:
            print("      ⚠️ Could not fetch sample price")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Market data connection error: {e}")
        return False

def create_demo_environment_file():
    """Create a .env file with demo configuration"""
    print("\n📝 Creating Demo Environment File...")
    
    env_content = """# Project Chimera - Live Paper Trading Configuration
# This file contains safe demo settings for paper trading

# Core Trading Settings
PAPER_TRADING_MODE=true
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
BORROW_AMOUNT_PER_TRADE=1000
MONITORING_INTERVAL_SECONDS=60
TAKE_PROFIT_DAYS_BEFORE_UNLOCK=1

# Mock Redis URL (services handle gracefully in paper mode)
REDIS_URL=redis://localhost:6379

# Optional: Add your API keys here for enhanced features
# COINGECKO_API_KEY=your_key_here
# TELEGRAM_BOT_TOKEN=your_bot_token_here
# TELEGRAM_CHAT_ID=your_chat_id_here

# Note: In paper trading mode, the system works safely without these keys
# using free tier APIs and mock data where needed
"""
    
    env_file = Path('.env')
    if not env_file.exists():
        with open(env_file, 'w') as f:
            f.write(env_content)
        print(f"   ✅ Created {env_file}")
        print("   💡 Edit this file to add your API keys for enhanced features")
    else:
        print(f"   ⚪ {env_file} already exists")
    
    return True

def run_quick_test():
    """Run a quick test of core functionality"""
    print("\n🧪 Running Quick Functionality Test...")
    
    try:
        # Test paper trading engine
        sys.path.insert(0, str(Path('services/the-executioner')))
        from paper_trading import paper_engine
        
        print("   📊 Testing paper trading engine...")
        portfolio = paper_engine.get_portfolio_summary()
        print(f"      ✅ Paper engine initialized: {portfolio['balances']}")
        
        # Test pressure score calculation
        sys.path.insert(0, str(Path('services/the-seer')))
        from analysis import calculate_unlock_pressure_score
        
        print("   🧠 Testing pressure score calculation...")
        test_event = {
            'token_symbol': 'TEST',
            'unlock_amount': 1000000,
            'circulating_supply': 10000000,
            'volume_24h': 5000000
        }
        
        score = calculate_unlock_pressure_score(test_event)
        print(f"      ✅ Pressure score calculated: {score:.4f}")
        
        # Test risk management
        sys.path.insert(0, str(Path('services/the-ledger')))
        from risk_manager import check_risk_rules
        
        print("   📊 Testing risk management...")
        test_position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.50',
            'unlock_date': '2025-12-01T00:00:00Z',
            'amount_shorted': '1000'
        }
        
        from decimal import Decimal
        action, reason = check_risk_rules(test_position, Decimal('2.40'))
        print(f"      ✅ Risk check completed: {action}")
        
        print("✅ All core functionality tests passed!")
        return True
        
    except Exception as e:
        print(f"   ❌ Quick test failed: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Project Chimera - Live Paper Trading Setup")
    print("=" * 60)
    
    checks = [
        ("Python Dependencies", check_python_dependencies),
        ("Environment Variables", setup_environment_variables),
        ("Service Files", check_service_files),
        ("Market Data Connection", test_market_data_connection),
        ("Demo Environment File", create_demo_environment_file),
        ("Quick Functionality Test", run_quick_test)
    ]
    
    all_passed = True
    
    for check_name, check_function in checks:
        try:
            result = check_function()
            if not result:
                all_passed = False
        except Exception as e:
            print(f"❌ {check_name} failed: {e}")
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 SETUP COMPLETE! Ready for live paper trading")
        print("\n🚀 Next steps:")
        print("   1. Run: python run_live_paper_trading.py")
        print("   2. Monitor the output for real-time trading activity")
        print("   3. Check generated log files for detailed results")
        print("\n💡 Tips:")
        print("   • Add API keys to .env file for enhanced features")
        print("   • Adjust parameters in .env for different strategies")
        print("   • All trades are simulated - no real money at risk")
    else:
        print("❌ SETUP INCOMPLETE - Please fix the issues above")
        print("\n🔧 Common fixes:")
        print("   • Install missing packages: pip install -r requirements.txt")
        print("   • Check internet connection for market data")
        print("   • Ensure all service files are present")

if __name__ == "__main__":
    main()
