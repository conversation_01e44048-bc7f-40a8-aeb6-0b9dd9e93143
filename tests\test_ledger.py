#!/usr/bin/env python3
"""
Tests for The Ledger service
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
from decimal import Decimal
from datetime import datetime, timezone, timedelta

# Add the ledger service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'services', 'the-ledger'))

from risk_manager import check_risk_rules, calculate_position_metrics
from price_fetcher import get_realtime_price

class TestRiskManager(unittest.TestCase):
    
    def test_stop_loss_trigger(self):
        """Test stop loss triggering for short position"""
        position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.00',
            'unlock_date': '2024-12-01T00:00:00Z'
        }
        
        # Price increased by 20% (loss for short position)
        current_price = Decimal('2.40')
        
        action, reason = check_risk_rules(position, current_price)
        
        self.assertEqual(action, "CLOSE")
        self.assertIn("Stop-Loss triggered", reason)
    
    def test_take_profit_price_trigger(self):
        """Test take profit triggering based on price"""
        position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.00',
            'unlock_date': '2024-12-01T00:00:00Z'
        }
        
        # Price decreased by 15% (profit for short position)
        current_price = Decimal('1.70')
        
        action, reason = check_risk_rules(position, current_price)
        
        self.assertEqual(action, "CLOSE")
        self.assertIn("Take-Profit triggered", reason)
    
    def test_time_based_exit(self):
        """Test time-based exit before unlock"""
        # Set unlock date to tomorrow
        tomorrow = datetime.now(timezone.utc) + timedelta(hours=12)
        
        position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.00',
            'unlock_date': tomorrow.isoformat()
        }
        
        # Price hasn't moved much
        current_price = Decimal('1.95')
        
        action, reason = check_risk_rules(position, current_price)
        
        self.assertEqual(action, "CLOSE")
        self.assertIn("Time-based exit", reason)
    
    def test_hold_position(self):
        """Test position should be held"""
        # Set unlock date to next month
        next_month = datetime.now(timezone.utc) + timedelta(days=30)
        
        position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.00',
            'unlock_date': next_month.isoformat()
        }
        
        # Price moved slightly down (small profit)
        current_price = Decimal('1.95')
        
        action, reason = check_risk_rules(position, current_price)
        
        self.assertEqual(action, "HOLD")
        self.assertIn("Current P&L", reason)
    
    def test_calculate_position_metrics(self):
        """Test position metrics calculation"""
        position = {
            'entry_price_in_usdc': '2.00',
            'amount_shorted': '1000'
        }
        
        current_price = Decimal('1.80')  # 10% price drop
        
        metrics = calculate_position_metrics(position, current_price)
        
        # Check required fields
        self.assertIn('pnl_pct', metrics)
        self.assertIn('pnl_usd', metrics)
        self.assertIn('stop_loss_price', metrics)
        self.assertIn('take_profit_price', metrics)
        
        # For a short position, 10% price drop should be 10% profit
        self.assertAlmostEqual(metrics['pnl_pct'], 10.0, places=1)
        
        # USD P&L should be positive (profit)
        self.assertGreater(metrics['pnl_usd'], 0)

class TestPriceFetcher(unittest.TestCase):
    
    @patch('price_fetcher.requests.get')
    def test_get_realtime_price_success(self, mock_get):
        """Test successful price fetching"""
        # Mock successful API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "0x1234567890123456789012345678901234567890": {
                "usd": 2.50
            }
        }
        mock_get.return_value = mock_response
        
        price = get_realtime_price("0x1234567890123456789012345678901234567890")
        
        self.assertIsInstance(price, Decimal)
        self.assertEqual(price, Decimal('2.50'))
    
    @patch('price_fetcher.requests.get')
    def test_get_realtime_price_api_error(self, mock_get):
        """Test price fetching with API error"""
        # Mock API error
        mock_get.side_effect = Exception("API Error")
        
        price = get_realtime_price("0x1234567890123456789012345678901234567890")
        
        self.assertIsNone(price)
    
    @patch('price_fetcher.requests.get')
    def test_get_realtime_price_no_data(self, mock_get):
        """Test price fetching when no data is returned"""
        # Mock response with no price data
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {}
        mock_get.return_value = mock_response
        
        price = get_realtime_price("0x1234567890123456789012345678901234567890")
        
        self.assertIsNone(price)

class TestLedgerIntegration(unittest.TestCase):
    
    @patch('price_fetcher.get_realtime_price')
    @patch('db_handler.get_open_positions')
    def test_monitoring_workflow(self, mock_get_positions, mock_get_price):
        """Test the complete monitoring workflow"""
        # Mock open positions
        mock_get_positions.return_value = [
            {
                'position_id': 1,
                'token_symbol': 'TEST1',
                'token_address': '0x1234567890123456789012345678901234567890',
                'entry_price_in_usdc': Decimal('2.00'),
                'unlock_date': '2024-12-01T00:00:00Z'
            },
            {
                'position_id': 2,
                'token_symbol': 'TEST2',
                'token_address': '0x0987654321098765432109876543210987654321',
                'entry_price_in_usdc': Decimal('1.50'),
                'unlock_date': '2024-12-01T00:00:00Z'
            }
        ]
        
        # Mock prices - one triggers stop loss, one is fine
        def mock_price_side_effect(token_address):
            if token_address == '0x1234567890123456789012345678901234567890':
                return Decimal('2.40')  # 20% increase - triggers stop loss
            else:
                return Decimal('1.45')  # 3% decrease - small profit
        
        mock_get_price.side_effect = mock_price_side_effect
        
        positions = mock_get_positions()
        
        results = []
        for pos in positions:
            current_price = mock_get_price(pos['token_address'])
            if current_price:
                action, reason = check_risk_rules(pos, current_price)
                results.append((pos['position_id'], action, reason))
        
        # Check results
        self.assertEqual(len(results), 2)
        
        # First position should trigger stop loss
        self.assertEqual(results[0][1], "CLOSE")
        self.assertIn("Stop-Loss", results[0][2])
        
        # Second position should hold (but might close due to time-based exit)
        self.assertIn(results[1][1], ["HOLD", "CLOSE"])

if __name__ == '__main__':
    unittest.main()
