#!/usr/bin/env python3
"""
Local testing script for Project Chimera services
Tests individual components without requiring full infrastructure
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_telegram_notifications():
    """Test Telegram bot functionality"""
    print("\n🔔 Testing Telegram Notifications...")
    
    try:
        sys.path.append('services/the-herald')
        from telegram_bot import send_telegram_message
        
        # Test message
        message = "🧪 Project Chimera Local Test\n✅ System is working!\n📅 " + str(os.environ.get('PAPER_TRADING_MODE', 'Unknown'))
        
        send_telegram_message(message)
        print("✅ Telegram message sent successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Telegram error: {e}")
        return False

def test_oracle_data_sources():
    """Test Oracle data source connections"""
    print("\n🔮 Testing Oracle Data Sources...")
    
    try:
        sys.path.append('services/the-oracle')
        from data_sources import fetch_token_unlocks_data
        
        print("📡 Fetching unlock data...")
        events = fetch_token_unlocks_data()
        
        print(f"✅ Found {len(events)} unlock events")
        if events:
            print(f"📋 Sample event: {events[0].get('token_symbol', 'Unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Oracle error: {e}")
        return False

def test_seer_analysis():
    """Test Seer analysis engine"""
    print("\n🧠 Testing Seer Analysis Engine...")
    
    try:
        sys.path.append('services/the-seer')
        from analysis import calculate_unlock_pressure_score
        
        # Test with sample data
        test_event = {
            'unlock_amount': 1000000,
            'circulating_supply': 100000000,
            'trading_volume_24h': 5000000
        }
        
        score = calculate_unlock_pressure_score(test_event)
        print(f"✅ Pressure score calculated: {score:.4f}")
        
        threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
        print(f"📊 Threshold: {threshold}")
        print(f"🎯 Trade signal: {'YES' if score >= threshold else 'NO'}")
        
        return True
        
    except Exception as e:
        print(f"❌ Seer error: {e}")
        return False

def test_paper_trading():
    """Test paper trading engine"""
    print("\n💰 Testing Paper Trading Engine...")
    
    try:
        sys.path.append('services/the-executioner')
        from paper_trading import paper_engine
        
        # Get portfolio status
        portfolio = paper_engine.get_portfolio_summary()
        
        print(f"✅ Paper trading engine active")
        print(f"💰 ETH Balance: {portfolio['balances']['eth']}")
        print(f"💵 USDC Balance: {portfolio['balances']['usdc']}")
        print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']}")
        print(f"📊 Total Trades: {portfolio['performance']['total_trades']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Paper trading error: {e}")
        return False

def test_configuration():
    """Test configuration and API keys"""
    print("\n⚙️ Testing Configuration...")
    
    # Check critical environment variables
    critical_vars = [
        'INFURA_API_KEY',
        'COINGECKO_API_KEY', 
        'ETHERSCAN_API_KEY',
        'TELEGRAM_BOT_TOKEN',
        'PAPER_TRADING_MODE'
    ]
    
    all_good = True
    for var in critical_vars:
        value = os.environ.get(var)
        if value and value != f'your_{var.lower()}_here':
            print(f"✅ {var}: Configured")
        else:
            print(f"❌ {var}: Missing or placeholder")
            all_good = False
    
    return all_good

def main():
    """Run all local tests"""
    print("🚀 Project Chimera - Local Service Testing")
    print("=" * 50)
    
    results = {}
    
    # Run tests
    results['config'] = test_configuration()
    results['oracle'] = test_oracle_data_sources()
    results['seer'] = test_seer_analysis()
    results['paper_trading'] = test_paper_trading()
    results['telegram'] = test_telegram_notifications()
    
    # Summary
    print("\n📋 Test Results Summary:")
    print("=" * 30)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name.upper()}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System ready for local testing.")
    else:
        print("⚠️ Some tests failed. Check configuration and dependencies.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
