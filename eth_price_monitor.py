#!/usr/bin/env python3
"""
Real-time Ethereum Price Monitor
Uses Binance WebSocket for live ETH price tracking
"""

import os
import sys
import time
import logging
from pathlib import Path
from decimal import Decimal
from datetime import datetime

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def format_price_change(current_price, previous_price):
    """Format price change with color indicators"""
    if previous_price is None:
        return ""
    
    change = current_price - previous_price
    change_pct = (change / previous_price) * 100
    
    if change > 0:
        return f"📈 +${change:.2f} (+{change_pct:.2f}%)"
    elif change < 0:
        return f"📉 ${change:.2f} ({change_pct:.2f}%)"
    else:
        return "➡️ No change"

def monitor_eth_price():
    """Monitor ETH price in real-time"""
    print("📊 Real-time Ethereum Price Monitor")
    print("🔗 Using Binance WebSocket for live data")
    print("=" * 50)
    
    # Set up logging
    logging.basicConfig(
        level=logging.WARNING,  # Reduce noise, only show warnings/errors
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
        
        # Initialize price feed
        feed = BinanceWebSocketPriceFeed(use_testnet=False)
        
        # Subscribe to ETH price
        print("📡 Connecting to Binance WebSocket...")
        success = feed.subscribe_to_price('ETH')
        
        if not success:
            print("❌ Failed to subscribe to ETH price feed")
            return
        
        print("✅ Connected! Monitoring ETH price...")
        print("💡 Press Ctrl+C to stop\n")
        
        previous_price = None
        update_count = 0
        
        try:
            while True:
                # Get current ETH price
                current_price = get_realtime_price_binance('ETH')
                
                if current_price:
                    update_count += 1
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    
                    # Format price change
                    change_info = format_price_change(current_price, previous_price)
                    
                    # Display price update
                    print(f"[{timestamp}] 💰 ETH: ${current_price:.2f} {change_info}")
                    
                    # Show additional info every 10 updates
                    if update_count % 10 == 0:
                        status = feed.get_status()
                        print(f"           📊 Updates: {update_count} | Connected: {status['connected']} | Reconnects: {status['reconnect_attempts']}")
                    
                    previous_price = current_price
                else:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ No price data available")
                
                time.sleep(2)  # Update every 2 seconds
                
        except KeyboardInterrupt:
            print("\n⏹️ Stopping price monitor...")
        
        finally:
            feed.stop()
            print("🛑 Price feed stopped")
            print(f"📊 Total updates received: {update_count}")
    
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure you're in the correct directory with the services folder")
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def get_current_eth_price():
    """Get current ETH price (single request)"""
    print("📊 Getting current Ethereum price...")
    
    try:
        from binance_websocket import get_realtime_price_binance
        
        # Get ETH price
        price = get_realtime_price_binance('ETH')
        
        if price:
            print(f"💰 Current ETH Price: ${price:.2f}")
            return price
        else:
            print("❌ Could not fetch ETH price")
            return None
    
    except Exception as e:
        print(f"❌ Error fetching ETH price: {e}")
        return None

def compare_price_sources():
    """Compare ETH prices from different sources"""
    print("🔍 Comparing ETH prices from different sources")
    print("=" * 45)
    
    try:
        # Test Binance WebSocket
        print("📡 Testing Binance WebSocket...")
        from binance_websocket import get_realtime_price_binance
        binance_price = get_realtime_price_binance('ETH')
        
        if binance_price:
            print(f"   ✅ Binance: ${binance_price:.2f}")
        else:
            print("   ❌ Binance: No data")
        
        # Test enhanced price fetcher (Binance + CoinGecko fallback)
        print("🔗 Testing enhanced price fetcher...")
        from price_fetcher import get_realtime_price
        
        # Try with ETH symbol
        enhanced_price = get_realtime_price('ETH')
        if enhanced_price:
            print(f"   ✅ Enhanced: ${enhanced_price:.2f}")
        else:
            print("   ❌ Enhanced: No data")
        
        # Try with WETH address
        weth_address = '******************************************'
        weth_price = get_realtime_price(weth_address)
        if weth_price:
            print(f"   ✅ WETH: ${weth_price:.2f}")
        else:
            print("   ❌ WETH: No data")
        
        # Compare prices
        if binance_price and enhanced_price:
            diff = abs(binance_price - enhanced_price)
            diff_pct = (diff / binance_price) * 100
            print(f"\n📊 Price difference: ${diff:.2f} ({diff_pct:.3f}%)")
            
            if diff_pct < 0.1:
                print("✅ Prices are very close - data sources are consistent")
            else:
                print("⚠️ Significant price difference detected")
    
    except Exception as e:
        print(f"❌ Error comparing prices: {e}")

def main():
    """Main menu for ETH price monitoring"""
    print("🚀 Ethereum Price Monitor - Project Chimera")
    print("=" * 50)
    
    while True:
        print("\nChoose an option:")
        print("1. 📊 Real-time ETH price monitoring")
        print("2. 💰 Get current ETH price (single request)")
        print("3. 🔍 Compare price sources")
        print("4. 🧪 Test WebSocket connection")
        print("5. ❌ Exit")
        
        try:
            choice = input("\nEnter your choice (1-5): ").strip()
            
            if choice == '1':
                monitor_eth_price()
            elif choice == '2':
                get_current_eth_price()
            elif choice == '3':
                compare_price_sources()
            elif choice == '4':
                test_websocket_connection()
            elif choice == '5':
                print("👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please enter 1-5.")
        
        except KeyboardInterrupt:
            print("\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")

def test_websocket_connection():
    """Test WebSocket connection health"""
    print("🧪 Testing WebSocket Connection Health")
    print("=" * 40)
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed
        
        # Create feed
        feed = BinanceWebSocketPriceFeed(use_testnet=False)
        
        # Test multiple symbols
        test_symbols = ['ETH', 'BTC', '******************************************']  # ETH, BTC, UNI
        
        print("📡 Testing subscriptions...")
        for symbol in test_symbols:
            success = feed.subscribe_to_price(symbol)
            symbol_name = symbol if len(symbol) < 10 else f"{symbol[:6]}...{symbol[-4:]}"
            print(f"   {'✅' if success else '❌'} {symbol_name}")
        
        # Wait for data
        print("\n⏱️ Waiting for price data...")
        time.sleep(5)
        
        # Check status
        status = feed.get_status()
        print(f"\n📊 Connection Status:")
        print(f"   🔗 Connected: {status['connected']}")
        print(f"   🏃 Running: {status['running']}")
        print(f"   📡 Subscribed: {len(status['subscribed_symbols'])} symbols")
        print(f"   💾 Active prices: {status['active_prices']}")
        
        if status['latest_prices']:
            print(f"   💰 Latest prices:")
            for symbol, price in status['latest_prices'].items():
                print(f"      {symbol}: {price}")
        
        # Clean up
        feed.stop()
        print("\n✅ WebSocket test completed")
    
    except Exception as e:
        print(f"❌ WebSocket test failed: {e}")

if __name__ == "__main__":
    main()
