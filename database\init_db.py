#!/usr/bin/env python3
"""
Database initialization script for Project Chimera
This script sets up the database schema and initial data
"""

import os
import sys
import psycopg2
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def get_database_url():
    """Get database URL from environment"""
    db_url = os.environ.get("DATABASE_URL")
    if not db_url:
        raise Exception("DATABASE_URL environment variable not set")
    return db_url

def read_schema_file():
    """Read the SQL schema file"""
    schema_path = Path(__file__).parent / "schema.sql"
    if not schema_path.exists():
        raise FileNotFoundError(f"Schema file not found: {schema_path}")
    
    with open(schema_path, 'r') as f:
        return f.read()

def initialize_database():
    """Initialize the database with schema and initial data"""
    try:
        # Connect to database
        db_url = get_database_url()
        logging.info("Connecting to database...")
        conn = psycopg2.connect(db_url)
        conn.autocommit = True
        
        # Read schema
        logging.info("Reading schema file...")
        schema_sql = read_schema_file()
        
        # Execute schema
        logging.info("Executing schema...")
        with conn.cursor() as cur:
            cur.execute(schema_sql)
        
        logging.info("Database initialization completed successfully!")
        
        # Verify tables were created
        with conn.cursor() as cur:
            cur.execute("""
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_type = 'BASE TABLE'
                ORDER BY table_name
            """)
            tables = cur.fetchall()
            
            logging.info("Created tables:")
            for table in tables:
                logging.info(f"  - {table[0]}")
        
        conn.close()
        
    except Exception as e:
        logging.error(f"Database initialization failed: {e}")
        sys.exit(1)

def check_database_connection():
    """Test database connection"""
    try:
        db_url = get_database_url()
        conn = psycopg2.connect(db_url)
        
        with conn.cursor() as cur:
            cur.execute("SELECT version()")
            version = cur.fetchone()[0]
            logging.info(f"Database connection successful. PostgreSQL version: {version}")
        
        conn.close()
        return True
        
    except Exception as e:
        logging.error(f"Database connection failed: {e}")
        return False

def reset_database():
    """Reset database by dropping and recreating all tables"""
    try:
        db_url = get_database_url()
        conn = psycopg2.connect(db_url)
        conn.autocommit = True
        
        logging.warning("Resetting database - this will delete all data!")
        
        with conn.cursor() as cur:
            # Drop all tables
            cur.execute("""
                DROP TABLE IF EXISTS risk_alerts CASCADE;
                DROP TABLE IF EXISTS system_events CASCADE;
                DROP TABLE IF EXISTS price_history CASCADE;
                DROP TABLE IF EXISTS positions CASCADE;
                DROP TABLE IF EXISTS unlock_events CASCADE;
            """)
            
            # Drop views
            cur.execute("""
                DROP VIEW IF EXISTS position_summary CASCADE;
                DROP VIEW IF EXISTS upcoming_unlocks CASCADE;
                DROP VIEW IF EXISTS open_positions CASCADE;
            """)
            
            # Drop functions
            cur.execute("DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;")
        
        logging.info("Database reset completed. Reinitializing...")
        conn.close()
        
        # Reinitialize
        initialize_database()
        
    except Exception as e:
        logging.error(f"Database reset failed: {e}")
        sys.exit(1)

def main():
    """Main function"""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == "init":
            initialize_database()
        elif command == "check":
            if check_database_connection():
                logging.info("Database check passed")
            else:
                sys.exit(1)
        elif command == "reset":
            reset_database()
        else:
            print("Usage: python init_db.py [init|check|reset]")
            print("  init  - Initialize database schema")
            print("  check - Check database connection")
            print("  reset - Reset database (WARNING: deletes all data)")
            sys.exit(1)
    else:
        # Default action is to initialize
        initialize_database()

if __name__ == "__main__":
    main()
