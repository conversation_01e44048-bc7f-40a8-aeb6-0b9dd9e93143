# Project Chimera Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/chimera_db
REDIS_URL=redis://localhost:6379

# Blockchain Configuration
INFURA_API_KEY=********************************
PRIVATE_KEY_PATH=/path/to/your/private/key/file

# Data Source APIs (Legacy - Deprecated)
TOKENUNLOCKS_API_KEY=your_tokenunlocks_api_key
VESTLAB_API_KEY=your_vestlab_api_key

# Market Data & Token Analytics APIs
COINGECKO_API_KEY=your_coingecko_api_key
DEXSCREENER_API_KEY=your_dexscreener_api_key
DEXTOOLS_API_KEY=your_dextools_api_key

# On-Chain Analytics APIs
NANSEN_API_KEY=your_nansen_api_key
GLASSNODE_API_KEY=your_glassnode_api_key
CRYPTOQUANT_API_KEY=your_cryptoquant_api_key
ETHERSCAN_API_KEY=your_etherscan_api_key

# Protocol Analytics APIs
DEFILLAMA_PRO_API_KEY=your_defillama_pro_api_key
TOKEN_TERMINAL_API_KEY=your_token_terminal_api_key
THEGRAPH_API_KEY=your_thegraph_api_key

# DEX & Trading APIs
ONEINCH_API_KEY=your_1inch_api_key

# Telegram Notifications
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=6049830025
TELEGRAM_ADMIN_CHAT_ID=6049830025
TELEGRAM_API_ID=29395164
TELEGRAM_API_HASH=4fd72e3993e581776c5aabd3c88771cc

# Risk Management Parameters
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
TAKE_PROFIT_DAYS_BEFORE_UNLOCK=1
BORROW_AMOUNT_PER_TRADE=1000
MONITORING_INTERVAL_SECONDS=60

# Development Settings
PYTHONPATH=.
LOGGING_LEVEL=INFO
PAPER_TRADING_MODE=true



# Project Chimera Environment Configuration - LOCAL TESTING
# DO NOT COMMIT THIS FILE TO VERSION CONTROL

# Database Configuration (for local testing - use mock/test DBs)
DATABASE_URL=postgresql://user:password@localhost:5432/chimera_test_db
REDIS_URL=redis://localhost:6379

# Blockchain Configuration - ACTIVE KEYS
INFURA_API_KEY=********************************
PRIVATE_KEY_PATH=/path/to/your/private/key/file

# Data Source APIs - DEPRECATED (services no longer operational)
TOKENUNLOCKS_API_KEY=deprecated_service_discontinued
VESTLAB_API_KEY=deprecated_domain_parked

# Working Data Sources (2025 Update)
COINGECKO_API_KEY=CG-F1RUwym6JGHQwgeivibNjuXN
ETHERSCAN_API_KEY=**********************************
DEXSCREENER_API_KEY=your_dexscreener_api_key_here
DEXTOOLS_API_KEY=your_dextools_api_key_here
DEFILLAMA_PRO_API_KEY=your_defillama_pro_api_key_here
THEGRAPH_API_KEY=your_thegraph_api_key_here
NANSEN_API_KEY=your_nansen_api_key_here
GLASSNODE_API_KEY=your_glassnode_api_key_here

# Telegram Notifications - ACTIVE CREDENTIALS
TELEGRAM_BOT_TOKEN=**********************************************
TELEGRAM_CHAT_ID=6049830025
TELEGRAM_ADMIN_CHAT_ID=6049830025
TELEGRAM_API_ID=29395164
TELEGRAM_API_HASH=4fd72e3993e581776c5aabd3c88771cc

# Risk Management Parameters
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
TAKE_PROFIT_DAYS_BEFORE_UNLOCK=1
BORROW_AMOUNT_PER_TRADE=1000
MONITORING_INTERVAL_SECONDS=60

# Development Settings
PYTHONPATH=.
LOGGING_LEVEL=INFO
PAPER_TRADING_MODE=true
