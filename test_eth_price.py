#!/usr/bin/env python3
"""
Simple ETH price test
"""

import sys
from pathlib import Path

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def test_eth_price():
    print("🧪 Testing ETH Price Fetching")
    print("=" * 30)
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed
        
        # Create feed
        feed = BinanceWebSocketPriceFeed(use_testnet=False)
        
        # Test symbol mapping
        print("🔍 Testing symbol mapping...")
        eth_symbol = feed.get_binance_symbol('ETH')
        print(f"ETH -> {eth_symbol}")
        
        ethereum_symbol = feed.get_binance_symbol('ETHEREUM')
        print(f"ETHEREUM -> {ethereum_symbol}")
        
        weth_symbol = feed.get_binance_symbol('******************************************')
        print(f"WETH -> {weth_symbol}")
        
        # Test direct REST API call
        print("\n🌐 Testing direct REST API...")
        rest_price = feed._get_price_rest_api('ETHUSDT')
        if rest_price:
            print(f"✅ ETH Price via REST: ${rest_price:.2f}")
        else:
            print("❌ REST API failed")
        
        # Test full price fetching
        print("\n📡 Testing full price fetching...")
        from binance_websocket import get_realtime_price_binance
        
        price = get_realtime_price_binance('ETH')
        if price:
            print(f"✅ ETH Price: ${price:.2f}")
        else:
            print("❌ Price fetch failed")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_eth_price()
