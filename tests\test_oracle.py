#!/usr/bin/env python3
"""
Tests for The Oracle service
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone

# Add the oracle service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'services', 'the-oracle'))

from data_sources import fetch_token_unlocks_data
from event_publisher import publish_unlock_event

class TestOracle(unittest.TestCase):
    
    def test_fetch_token_unlocks_data(self):
        """Test that data fetching returns expected format"""
        data = fetch_token_unlocks_data()
        
        self.assertIsInstance(data, list)
        if data:  # If mock data is returned
            event = data[0]
            self.assertIn('token_symbol', event)
            self.assertIn('contract_address', event)
            self.assertIn('unlock_date', event)
            self.assertIn('unlock_amount', event)
    
    @patch('event_publisher.get_redis_connection')
    def test_publish_unlock_event(self, mock_get_redis):
        """Test event publishing"""
        mock_redis_instance = MagicMock()
        mock_get_redis.return_value = mock_redis_instance

        test_event = {
            'token_symbol': 'TEST',
            'contract_address': '0x123',
            'unlock_date': '2024-12-01T00:00:00Z',
            'unlock_amount': 1000000
        }

        publish_unlock_event(test_event)

        # Verify Redis publish was called
        mock_redis_instance.publish.assert_called_once()

        # Check the channel and message
        call_args = mock_redis_instance.publish.call_args
        channel = call_args[0][0]
        message = call_args[0][1]

        self.assertEqual(channel, "chimera:unlock_events")
        self.assertIn('TEST', message)

class TestDataSources(unittest.TestCase):
    
    def test_mock_data_structure(self):
        """Test that mock data has correct structure"""
        data = fetch_token_unlocks_data()
        
        for event in data:
            # Required fields
            self.assertIn('token_symbol', event)
            self.assertIn('contract_address', event)
            self.assertIn('unlock_date', event)
            self.assertIn('unlock_amount', event)
            self.assertIn('source', event)
            
            # Data types
            self.assertIsInstance(event['token_symbol'], str)
            self.assertIsInstance(event['contract_address'], str)
            self.assertIsInstance(event['unlock_date'], str)
            self.assertIsInstance(event['unlock_amount'], (int, float))
            
            # Contract address format (basic check)
            self.assertTrue(event['contract_address'].startswith('0x'))
            self.assertGreaterEqual(len(event['contract_address']), 40)  # Allow for different lengths

if __name__ == '__main__':
    unittest.main()
