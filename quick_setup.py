#!/usr/bin/env python3
"""
Quick Setup Script for Project Chimera
Sets up the free/testing components to get you started quickly.
"""

import os
import sys
import subprocess
from pathlib import Path

def run_command(cmd, check=True):
    """Run a shell command"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, check=check, capture_output=True, text=True)
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr)
    return result

def setup_env_file():
    """Create .env file with basic configuration"""
    print("\n📝 Setting up .env file...")
    
    env_content = """# Project Chimera Environment Configuration
# Basic setup for testing - replace with real values for production

# Database Configuration (local testing)
DATABASE_URL=postgresql://postgres:password@localhost:5432/chimera_test
REDIS_URL=redis://localhost:6379

# Blockchain Configuration (REPLACE WITH YOUR VALUES)
INFURA_API_KEY=your_infura_api_key_here
PRIVATE_KEY_PATH=/path/to/your/private/key/file

# Data Source APIs (REQUIRED FOR PRODUCTION)
TOKENUNLOCKS_API_KEY=your_tokenunlocks_api_key
VESTLAB_API_KEY=your_vestlab_api_key

# Trading Parameters (safe defaults for testing)
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
BORROW_AMOUNT_PER_TRADE=1000
PAPER_TRADING_MODE=true

# Optional APIs (can be added later)
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id
COINGECKO_API_KEY=your_coingecko_api_key
ONEINCH_API_KEY=your_1inch_api_key

# Development Settings
PYTHONPATH=.
LOGGING_LEVEL=INFO
"""
    
    env_path = Path(".env")
    if env_path.exists():
        print("⚠️  .env file already exists. Backing up to .env.backup")
        env_path.rename(".env.backup")
    
    with open(".env", "w") as f:
        f.write(env_content)
    
    print("✅ Created .env file with basic configuration")
    print("🔧 Edit .env file to add your API keys")

def check_docker():
    """Check if Docker is available"""
    try:
        result = run_command(["docker", "--version"], check=False)
        if result.returncode == 0:
            print("✅ Docker is available")
            return True
        else:
            print("❌ Docker not found")
            return False
    except FileNotFoundError:
        print("❌ Docker not found")
        return False

def setup_local_infrastructure():
    """Set up local PostgreSQL and Redis using Docker"""
    print("\n🐳 Setting up local infrastructure with Docker...")
    
    if not check_docker():
        print("⚠️  Docker not available. Please install Docker or set up PostgreSQL/Redis manually.")
        print("   PostgreSQL: https://postgresql.org/download/")
        print("   Redis: https://redis.io/download")
        return False
    
    # Check if containers already exist
    try:
        # Stop existing containers if they exist
        run_command(["docker", "stop", "chimera-postgres"], check=False)
        run_command(["docker", "stop", "chimera-redis"], check=False)
        run_command(["docker", "rm", "chimera-postgres"], check=False)
        run_command(["docker", "rm", "chimera-redis"], check=False)
        
        # Start PostgreSQL
        print("🗄️  Starting PostgreSQL...")
        run_command([
            "docker", "run", "-d",
            "--name", "chimera-postgres",
            "-p", "5432:5432",
            "-e", "POSTGRES_PASSWORD=password",
            "-e", "POSTGRES_DB=chimera_test",
            "postgres:13"
        ])
        
        # Start Redis
        print("🔄 Starting Redis...")
        run_command([
            "docker", "run", "-d",
            "--name", "chimera-redis",
            "-p", "6379:6379",
            "redis:7"
        ])
        
        print("✅ Local infrastructure started successfully!")
        print("   PostgreSQL: localhost:5432 (user: postgres, password: password)")
        print("   Redis: localhost:6379")
        
        return True
        
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to start infrastructure: {e}")
        return False

def install_dependencies():
    """Install Python dependencies"""
    print("\n📦 Installing Python dependencies...")
    
    try:
        run_command([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install dependencies")
        return False

def create_database_schema():
    """Create database schema"""
    print("\n🗄️  Creating database schema...")
    
    try:
        # Wait a moment for PostgreSQL to start
        import time
        time.sleep(3)
        
        # Try to create the schema
        schema_path = Path("database/schema.sql")
        if schema_path.exists():
            # Use psql to create schema
            run_command([
                "docker", "exec", "-i", "chimera-postgres",
                "psql", "-U", "postgres", "-d", "chimera_test"
            ], check=False)  # This might fail if psql not available
            
            print("✅ Database schema creation attempted")
        else:
            print("⚠️  database/schema.sql not found")
            
    except Exception as e:
        print(f"⚠️  Could not create database schema automatically: {e}")
        print("   You can create it manually later using database/schema.sql")

def run_tests():
    """Run basic tests to verify setup"""
    print("\n🧪 Running basic tests...")
    
    try:
        # Run our custom integration test
        result = run_command([sys.executable, "test_aave_integration.py"], check=False)
        if result.returncode == 0:
            print("✅ Basic integration test passed")
        else:
            print("⚠️  Integration test had issues (expected without API keys)")
        
        # Run a simple pytest
        result = run_command([sys.executable, "-m", "pytest", "tests/test_seer.py", "-v"], check=False)
        if result.returncode == 0:
            print("✅ Core logic tests passed")
        else:
            print("⚠️  Some tests failed (may need API keys)")
            
    except Exception as e:
        print(f"⚠️  Could not run tests: {e}")

def print_next_steps():
    """Print what to do next"""
    print("\n🎯 Next Steps:")
    print("=" * 50)
    print("1. 🔑 Get API Keys:")
    print("   • Infura: https://infura.io (free)")
    print("   • CoinGecko: https://coingecko.com/api (free tier)")
    print("   • 1inch: https://1inch.io/api (free tier)")
    print("   • Telegram: @BotFather (free)")
    print()
    print("2. 💰 For Production (Paid APIs):")
    print("   • TokenUnlocks: https://tokenunlocks.com/api (~$75/month)")
    print("   • Vestlab: https://vestlab.io (~$50/month)")
    print()
    print("3. 🔒 Security Setup:")
    print("   • Create a new Ethereum wallet for trading")
    print("   • Store private key securely (never commit to git)")
    print("   • Fund wallet with ETH for gas fees")
    print()
    print("4. 🧪 Testing:")
    print("   • Keep PAPER_TRADING_MODE=true for testing")
    print("   • Run: python check_configuration.py")
    print("   • Run: python -m pytest tests/ -v")
    print()
    print("5. 🚀 Deployment:")
    print("   • Update render.yaml with your GitHub repo")
    print("   • Deploy to Render.com (free tier available)")
    print("   • Set environment variables in Render dashboard")

def main():
    """Main setup function"""
    print("🚀 Project Chimera - Quick Setup")
    print("=" * 40)
    print("This script will set up the basic components for testing.")
    print("You'll still need to get API keys for full functionality.")
    print()
    
    # Check if we're in the right directory
    if not Path("render.yaml").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    success_count = 0
    total_steps = 5
    
    # Step 1: Create .env file
    try:
        setup_env_file()
        success_count += 1
    except Exception as e:
        print(f"❌ Failed to setup .env file: {e}")
    
    # Step 2: Install dependencies
    try:
        if install_dependencies():
            success_count += 1
    except Exception as e:
        print(f"❌ Failed to install dependencies: {e}")
    
    # Step 3: Setup infrastructure
    try:
        if setup_local_infrastructure():
            success_count += 1
    except Exception as e:
        print(f"❌ Failed to setup infrastructure: {e}")
    
    # Step 4: Create database schema
    try:
        create_database_schema()
        success_count += 1
    except Exception as e:
        print(f"❌ Failed to create database schema: {e}")
    
    # Step 5: Run tests
    try:
        run_tests()
        success_count += 1
    except Exception as e:
        print(f"❌ Failed to run tests: {e}")
    
    # Summary
    print(f"\n📊 Setup Summary: {success_count}/{total_steps} steps completed")
    
    if success_count >= 3:
        print("🎉 Basic setup completed successfully!")
    else:
        print("⚠️  Setup had some issues. Check the output above.")
    
    print_next_steps()

if __name__ == "__main__":
    main()
