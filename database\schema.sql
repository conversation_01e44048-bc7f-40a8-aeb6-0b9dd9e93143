-- Project Chimera Database Schema
-- PostgreSQL schema for storing token unlock events and trading positions

-- Create extension for UUID generation if not exists
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Table for storing token unlock events
CREATE TABLE IF NOT EXISTS unlock_events (
    id SERIAL PRIMARY KEY,
    token_symbol VARCHAR(20) NOT NULL,
    contract_address VARCHAR(42) NOT NULL,
    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
    unlock_amount DECIMAL NOT NULL,
    circulating_supply DECIMAL,
    total_supply DECIMAL,
    source VARCHAR(50),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Ensure no duplicate events for same token and date
    UNIQUE(contract_address, unlock_date)
);

-- Table for storing trading positions
CREATE TABLE IF NOT EXISTS positions (
    id SERIAL PRIMARY KEY,
    token_symbol VARCHAR(20) NOT NULL,
    token_address VARCHAR(42) NOT NULL,
    amount_shorted DECIMAL NOT NULL,
    entry_price_in_usdc DECIMAL NOT NULL,
    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
    status VARCHAR(20) DEFAULT 'OPEN' CHECK (status IN ('OPEN', 'CLOSING', 'CLOSED', 'FAILED')),
    
    -- Transaction hashes for tracking on-chain activity
    borrow_tx_hash VARCHAR(66),
    swap_tx_hash VARCHAR(66),
    close_tx_hash VARCHAR(66),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    closed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Additional metadata
    strategy_id VARCHAR(50) DEFAULT 'pre_unlock_decay_v1',
    pressure_score DECIMAL,
    notes TEXT
);

-- Table for storing price history (for analysis and backtesting)
CREATE TABLE IF NOT EXISTS price_history (
    id SERIAL PRIMARY KEY,
    token_address VARCHAR(42) NOT NULL,
    price_usd DECIMAL NOT NULL,
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    source VARCHAR(50) DEFAULT 'coingecko',
    
    -- Index for efficient querying
    INDEX idx_price_history_token_time (token_address, timestamp)
);

-- Table for storing system events and logs
CREATE TABLE IF NOT EXISTS system_events (
    id SERIAL PRIMARY KEY,
    event_type VARCHAR(50) NOT NULL,
    event_data JSONB,
    service_name VARCHAR(50),
    timestamp TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Index for efficient querying by type and time
    INDEX idx_system_events_type_time (event_type, timestamp)
);

-- Table for storing risk management alerts
CREATE TABLE IF NOT EXISTS risk_alerts (
    id SERIAL PRIMARY KEY,
    position_id INTEGER REFERENCES positions(id),
    alert_type VARCHAR(50) NOT NULL,
    alert_message TEXT,
    current_price DECIMAL,
    trigger_price DECIMAL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    acknowledged BOOLEAN DEFAULT FALSE,
    
    -- Index for efficient querying
    INDEX idx_risk_alerts_position (position_id),
    INDEX idx_risk_alerts_type_time (alert_type, created_at)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_unlock_events_date ON unlock_events(unlock_date);
CREATE INDEX IF NOT EXISTS idx_unlock_events_token ON unlock_events(contract_address);
CREATE INDEX IF NOT EXISTS idx_unlock_events_symbol ON unlock_events(token_symbol);

CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status);
CREATE INDEX IF NOT EXISTS idx_positions_token ON positions(token_address);
CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(token_symbol);
CREATE INDEX IF NOT EXISTS idx_positions_unlock_date ON positions(unlock_date);
CREATE INDEX IF NOT EXISTS idx_positions_created_at ON positions(created_at);

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers to automatically update updated_at
CREATE TRIGGER update_unlock_events_updated_at 
    BEFORE UPDATE ON unlock_events 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_positions_updated_at 
    BEFORE UPDATE ON positions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create views for common queries
CREATE OR REPLACE VIEW open_positions AS
SELECT 
    id as position_id,
    token_symbol,
    token_address,
    amount_shorted,
    entry_price_in_usdc,
    unlock_date,
    status,
    borrow_tx_hash,
    swap_tx_hash,
    created_at,
    pressure_score,
    EXTRACT(EPOCH FROM (unlock_date - NOW())) / 86400 as days_to_unlock
FROM positions 
WHERE status = 'OPEN'
ORDER BY created_at ASC;

CREATE OR REPLACE VIEW upcoming_unlocks AS
SELECT 
    token_symbol,
    contract_address,
    unlock_date,
    unlock_amount,
    circulating_supply,
    total_supply,
    source,
    EXTRACT(EPOCH FROM (unlock_date - NOW())) / 86400 as days_until_unlock,
    (unlock_amount / NULLIF(circulating_supply, 0)) * 100 as unlock_percentage
FROM unlock_events 
WHERE unlock_date > NOW() 
AND unlock_date <= NOW() + INTERVAL '30 days'
ORDER BY unlock_date ASC;

CREATE OR REPLACE VIEW position_summary AS
SELECT 
    status,
    COUNT(*) as count,
    SUM(amount_shorted * entry_price_in_usdc) as total_value_usd,
    AVG(entry_price_in_usdc) as avg_entry_price,
    MIN(created_at) as oldest_position,
    MAX(created_at) as newest_position
FROM positions 
GROUP BY status;

-- Insert some initial data for testing (optional)
-- This can be removed in production
INSERT INTO unlock_events (
    token_symbol, contract_address, unlock_date, unlock_amount, 
    circulating_supply, total_supply, source
) VALUES 
(
    'DYDX', 
    '0x92D6C1e31e14519D225d5829CF70AF773944W7f', 
    '2024-12-01T00:00:00Z', 
    150000000.0, 
    300000000.0, 
    1000000000.0, 
    'TokenUnlocks.com'
),
(
    'UNI', 
    '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984', 
    '2024-11-15T00:00:00Z', 
    83333333.0, 
    750000000.0, 
    1000000000.0, 
    'Vestlab.io'
)
ON CONFLICT (contract_address, unlock_date) DO NOTHING;

-- Grant permissions (adjust as needed for your setup)
-- GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO chimera_user;
-- GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO chimera_user;
