"""
Enhanced Error Handling Module - 2025 Best Practices
=====================================================

This module provides centralized error handling, retry mechanisms, and circuit breaker patterns
for the Project Chimera trading system. It implements modern resilience patterns to ensure
system stability and graceful degradation under various failure conditions.

Features:
- Exponential backoff retry mechanisms
- Circuit breaker pattern for external APIs
- Structured error logging with context
- Custom exception hierarchy
- Rate limiting and throttling
- Health check utilities
"""

import asyncio
import logging
import time
import functools
from typing import Any, Callable, Dict, List, Optional, Type, Union
from datetime import datetime, timedelta
from enum import Enum
import random


class ErrorSeverity(Enum):
    """Error severity levels for classification and handling"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ChimeraError(Exception):
    """Base exception class for Project Chimera"""
    def __init__(self, message: str, severity: ErrorSeverity = ErrorSeverity.MEDIUM, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.severity = severity
        self.context = context or {}
        self.timestamp = datetime.utcnow()


class DataSourceError(ChimeraError):
    """Raised when external data source fails"""
    def __init__(self, source: str, message: str, **kwargs):
        super().__init__(f"Data source '{source}' error: {message}", **kwargs)
        self.source = source


class TradingError(ChimeraError):
    """Raised when trading operations fail"""
    def __init__(self, operation: str, message: str, **kwargs):
        super().__init__(f"Trading operation '{operation}' failed: {message}", **kwargs)
        self.operation = operation


class RiskManagementError(ChimeraError):
    """Raised when risk management rules are violated"""
    def __init__(self, rule: str, message: str, **kwargs):
        super().__init__(f"Risk rule '{rule}' violation: {message}", 
                         severity=ErrorSeverity.HIGH, **kwargs)
        self.rule = rule


class CircuitBreakerError(ChimeraError):
    """Raised when circuit breaker is open"""
    def __init__(self, service: str, **kwargs):
        super().__init__(f"Circuit breaker open for service: {service}", 
                         severity=ErrorSeverity.HIGH, **kwargs)
        self.service = service


class CircuitBreaker:
    """
    Circuit breaker implementation for external service calls.
    
    Prevents cascading failures by temporarily disabling calls to failing services.
    """
    
    def __init__(self, failure_threshold: int = 5, recovery_timeout: int = 60, 
                 expected_exception: Type[Exception] = Exception):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN
        
    def call(self, func: Callable, *args, **kwargs):
        """Execute function with circuit breaker protection"""
        if self.state == 'OPEN':
            if self._should_attempt_reset():
                self.state = 'HALF_OPEN'
            else:
                raise CircuitBreakerError(func.__name__)
        
        try:
            result = func(*args, **kwargs)
            self._on_success()
            return result
        except self.expected_exception as e:
            self._on_failure()
            raise e
    
    def _should_attempt_reset(self) -> bool:
        """Check if enough time has passed to attempt reset"""
        return (time.time() - self.last_failure_time) >= self.recovery_timeout
    
    def _on_success(self):
        """Handle successful call"""
        self.failure_count = 0
        self.state = 'CLOSED'
    
    def _on_failure(self):
        """Handle failed call"""
        self.failure_count += 1
        self.last_failure_time = time.time()
        
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'


def retry_with_backoff(max_retries: int = 3, base_delay: float = 1.0, 
                      max_delay: float = 60.0, backoff_factor: float = 2.0,
                      jitter: bool = True, exceptions: tuple = (Exception,)):
    """
    Decorator for exponential backoff retry with jitter.
    
    Args:
        max_retries: Maximum number of retry attempts
        base_delay: Initial delay between retries (seconds)
        max_delay: Maximum delay between retries (seconds)
        backoff_factor: Multiplier for exponential backoff
        jitter: Add random jitter to prevent thundering herd
        exceptions: Tuple of exceptions to catch and retry
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_retries:
                        logging.error(f"Function {func.__name__} failed after {max_retries} retries: {e}")
                        raise e
                    
                    # Calculate delay with exponential backoff
                    delay = min(base_delay * (backoff_factor ** attempt), max_delay)
                    
                    # Add jitter to prevent thundering herd
                    if jitter:
                        delay *= (0.5 + random.random() * 0.5)
                    
                    logging.warning(f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                                  f"Retrying in {delay:.2f} seconds...")
                    time.sleep(delay)
            
            raise last_exception
        
        return wrapper
    return decorator


class RateLimiter:
    """
    Token bucket rate limiter for API calls.
    """
    
    def __init__(self, max_tokens: int, refill_rate: float):
        self.max_tokens = max_tokens
        self.tokens = max_tokens
        self.refill_rate = refill_rate
        self.last_refill = time.time()
    
    def acquire(self, tokens: int = 1) -> bool:
        """Attempt to acquire tokens from the bucket"""
        self._refill()
        
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        return False
    
    def _refill(self):
        """Refill tokens based on elapsed time"""
        now = time.time()
        elapsed = now - self.last_refill
        tokens_to_add = elapsed * self.refill_rate
        
        self.tokens = min(self.max_tokens, self.tokens + tokens_to_add)
        self.last_refill = now


def safe_api_call(circuit_breaker: Optional[CircuitBreaker] = None,
                 rate_limiter: Optional[RateLimiter] = None,
                 timeout: float = 30.0):
    """
    Decorator for safe API calls with circuit breaker and rate limiting.
    
    Args:
        circuit_breaker: Circuit breaker instance
        rate_limiter: Rate limiter instance
        timeout: Request timeout in seconds
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Check rate limiter
            if rate_limiter and not rate_limiter.acquire():
                raise ChimeraError("Rate limit exceeded", severity=ErrorSeverity.MEDIUM)
            
            # Add timeout to kwargs if not present
            if 'timeout' not in kwargs:
                kwargs['timeout'] = timeout
            
            # Use circuit breaker if provided
            if circuit_breaker:
                return circuit_breaker.call(func, *args, **kwargs)
            else:
                return func(*args, **kwargs)
        
        return wrapper
    return decorator


def log_error_with_context(error: Exception, context: Dict[str, Any], 
                          logger: Optional[logging.Logger] = None):
    """
    Log error with structured context information.
    
    Args:
        error: Exception to log
        context: Additional context information
        logger: Logger instance (uses root logger if None)
    """
    if logger is None:
        logger = logging.getLogger()
    
    error_info = {
        'error_type': type(error).__name__,
        'error_message': str(error),
        'timestamp': datetime.utcnow().isoformat(),
        'context': context
    }
    
    if isinstance(error, ChimeraError):
        error_info['severity'] = error.severity.value
        error_info['chimera_context'] = error.context
    
    logger.error(f"Error occurred: {error_info}")


class HealthChecker:
    """
    Health check utility for monitoring system components.
    """
    
    def __init__(self):
        self.checks = {}
        self.last_check_time = {}
    
    def register_check(self, name: str, check_func: Callable[[], bool], 
                      interval: int = 60):
        """Register a health check function"""
        self.checks[name] = {
            'func': check_func,
            'interval': interval,
            'last_result': None,
            'last_error': None
        }
    
    def run_check(self, name: str) -> Dict[str, Any]:
        """Run a specific health check"""
        if name not in self.checks:
            return {'status': 'unknown', 'error': f'Check {name} not registered'}
        
        check = self.checks[name]
        
        try:
            result = check['func']()
            check['last_result'] = result
            check['last_error'] = None
            
            return {
                'status': 'healthy' if result else 'unhealthy',
                'timestamp': datetime.utcnow().isoformat(),
                'result': result
            }
        except Exception as e:
            check['last_error'] = str(e)
            return {
                'status': 'error',
                'timestamp': datetime.utcnow().isoformat(),
                'error': str(e)
            }
    
    def run_all_checks(self) -> Dict[str, Any]:
        """Run all registered health checks"""
        results = {}
        overall_status = 'healthy'
        
        for name in self.checks:
            result = self.run_check(name)
            results[name] = result
            
            if result['status'] != 'healthy':
                overall_status = 'unhealthy'
        
        return {
            'overall_status': overall_status,
            'timestamp': datetime.utcnow().isoformat(),
            'checks': results
        }


# Global instances for common use
default_circuit_breaker = CircuitBreaker(failure_threshold=3, recovery_timeout=30)
api_rate_limiter = RateLimiter(max_tokens=100, refill_rate=10.0)  # 10 requests per second
health_checker = HealthChecker()
