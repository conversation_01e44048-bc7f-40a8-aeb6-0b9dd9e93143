# render.yaml
# Blueprint for deploying the entire Project Chimera microservices suite on Render.com.
# This file defines all services, databases, and cron jobs, emphasizing free-tier usage.

version: 1

# Environment Group for shared secrets and configs across all services
# These values should be set in the Render dashboard under Environment Groups.
envVarGroups:
  - name: chimera-shared-env
    envVars:
      - key: DATABASE_URL
        fromDatabase:
          name: chimera-db
          property: connectionString
      - key: REDIS_URL
        fromDatabase:
          name: chimera-redis
          property: connectionString
      - key: PYTHON_VERSION
        value: "3.10"
      # Risk management parameters
      - key: PRESSURE_SCORE_THRESHOLD
        value: "0.75"
      - key: STOP_LOSS_PCT
        value: "0.15"
      - key: TAKE_PROFIT_PCT
        value: "0.10"
      - key: TAKE_PROFIT_DAYS_BEFORE_UNLOCK
        value: "1"
      - key: BORROW_AMOUNT_PER_TRADE
        value: "1000"
      - key: MONITORING_INTERVAL_SECONDS
        value: "60"
      # --- SECRETS (SET IN RENDER DASHBOARD) ---
      - key: INFURA_API_KEY
        sync: false
      - key: TELEGRAM_BOT_TOKEN
        sync: false
      - key: TELEGRAM_CHAT_ID
        sync: false
      - key: TELEGRAM_ADMIN_CHAT_ID
        sync: false
      - key: TELEGRAM_API_ID
        sync: false
      - key: TELEGRAM_API_HASH
        sync: false
      # --- OPTIONAL DATA SOURCE APIS ---
      # Legacy APIs (Deprecated)
      - key: TOKENUNLOCKS_API_KEY
        sync: false
      - key: VESTLAB_API_KEY
        sync: false
      # Market Data & Token Analytics
      - key: COINGECKO_API_KEY
        sync: false
      - key: DEXSCREENER_API_KEY
        sync: false
      - key: DEXTOOLS_API_KEY
        sync: false
      # On-Chain Analytics
      - key: NANSEN_API_KEY
        sync: false
      - key: GLASSNODE_API_KEY
        sync: false
      - key: CRYPTOQUANT_API_KEY
        sync: false
      - key: ETHERSCAN_API_KEY
        sync: false
      # Protocol Analytics
      - key: DEFILLAMA_PRO_API_KEY
        sync: false
      - key: TOKEN_TERMINAL_API_KEY
        sync: false
      - key: THEGRAPH_API_KEY
        sync: false
      # DEX & Trading
      - key: ONEINCH_API_KEY
        sync: false

databases:
  - name: chimera-db
    databaseName: chimera_db
    plan: free # Use the free PostgreSQL instance
    ipAllowList: [] # Allow access from all services

  - name: chimera-redis
    plan: free # Use the free Redis instance
    ipAllowList: [] # Allow access from all services

services:
  # Service: The Seer (Analytical & Strategy Engine)
  - name: the-seer
    type: pserv # Private Service (worker)
    plan: free
    env: docker
    repo: https://github.com/your-username/project-chimera # <-- CHANGE THIS
    rootDir: ./services/the-seer
    dockerfilePath: ./services/the-seer/Dockerfile
    envVarGroups:
      - chimera-shared-env

  # Service: The Executioner (Trade Execution Engine)
  - name: the-executioner
    type: pserv
    plan: free
    env: docker
    repo: https://github.com/your-username/project-chimera # <-- CHANGE THIS
    rootDir: ./services/the-executioner
    dockerfilePath: ./services/the-executioner/Dockerfile
    envVarGroups:
      - chimera-shared-env
    secretFiles:
      - key: PRIVATE_KEY_PATH
        path: /etc/secrets/trader-pk # Mounts the secret file at this path

  # Service: The Ledger (Risk & Portfolio Management)
  - name: the-ledger
    type: pserv
    plan: free
    env: docker
    repo: https://github.com/your-username/project-chimera # <-- CHANGE THIS
    rootDir: ./services/the-ledger
    dockerfilePath: ./services/the-ledger/Dockerfile
    envVarGroups:
      - chimera-shared-env

  # Service: The Herald (Notification Service)
  - name: the-herald
    type: pserv
    plan: free
    env: docker
    repo: https://github.com/your-username/project-chimera # <-- CHANGE THIS
    rootDir: ./services/the-herald
    dockerfilePath: ./services/the-herald/Dockerfile
    envVarGroups:
      - chimera-shared-env

cronJobs:
  # Cron Job: The Oracle (Data Ingestion & Event Triggering)
  - name: the-oracle-cron
    plan: free
    schedule: "0 1 * * *" # Run daily at 1:00 AM UTC
    env: docker
    repo: https://github.com/your-username/project-chimera # <-- CHANGE THIS
    rootDir: ./services/the-oracle
    dockerfilePath: ./services/the-oracle/Dockerfile
    command: "python main.py"
    envVarGroups:
      - chimera-shared-env
