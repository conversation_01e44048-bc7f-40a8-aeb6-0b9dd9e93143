#!/usr/bin/env python3
"""
Binance WebSocket Integration - 2025 Enhanced Version
Implements latest Binance API best practices with robust error handling
"""

import json
import logging
import threading
import time
import asyncio
from typing import Dict, List, Callable, Optional
from decimal import Decimal
import websocket
from datetime import datetime, timedelta

class BinanceWebSocket2025:
    """
    Enhanced Binance WebSocket manager for 2025
    Implements all latest Binance API requirements and best practices
    """
    
    def __init__(self):
        # Connection management
        self.ws = None
        self.is_connected = False
        self.connection_start_time = None
        self.last_ping_time = None
        self.last_pong_time = None
        
        # Data storage
        self.price_data = {}
        self.price_history = {}
        self.callbacks = []
        self.monitored_symbols = set()
        
        # Connection settings (2025 Binance requirements)
        self.base_url = "wss://stream.binance.com:9443"
        self.connection_timeout = 24 * 60 * 60  # 24 hours max connection time
        self.ping_interval = 20  # Binance sends ping every 20 seconds
        self.pong_timeout = 60   # Must respond within 1 minute
        self.rate_limit_delay = 0.2  # 5 messages per second = 200ms between messages
        
        # Reconnection settings
        self.connection_attempts = 0
        self.max_connection_attempts = 10
        self.reconnect_delays = [1, 2, 5, 10, 30, 60, 120, 300, 600, 900]  # Progressive backoff
        
        # Health monitoring
        self.health_check_interval = 300  # Check connection health every 5 minutes
        self.last_message_time = None
        
        logging.info("🚀 Binance WebSocket 2025 initialized")
    
    def add_symbols(self, symbols: List[str]):
        """Add multiple symbols to monitor"""
        for symbol in symbols:
            self.add_symbol(symbol)
    
    def add_symbol(self, symbol: str):
        """Add a symbol to monitor with validation"""
        symbol = symbol.upper().strip()
        if not symbol.endswith('USDT'):
            symbol += 'USDT'
        
        # Validate symbol format
        if len(symbol) < 6 or not symbol.replace('USDT', '').isalpha():
            logging.warning(f"⚠️ Invalid symbol format: {symbol}")
            return False
        
        self.monitored_symbols.add(symbol)
        logging.info(f"✅ Added {symbol} to monitoring ({len(self.monitored_symbols)} total)")
        return True
    
    def start_monitoring(self):
        """Start monitoring with enhanced error handling"""
        if not self.monitored_symbols:
            logging.warning("⚠️ No symbols to monitor")
            return False
        
        if self.is_connected:
            logging.info("ℹ️ Already connected")
            return True
        
        logging.info(f"🔄 Starting Binance WebSocket monitoring for {len(self.monitored_symbols)} symbols")
        self.connection_attempts = 0
        return self._connect()
    
    def _connect(self):
        """Enhanced connection method with 2025 best practices"""
        try:
            if self.connection_attempts >= self.max_connection_attempts:
                logging.error(f"❌ Max connection attempts ({self.max_connection_attempts}) reached")
                return False
            
            # Build stream URL for multiple symbols
            streams = [f"{symbol.lower()}@ticker" for symbol in self.monitored_symbols]
            stream_url = f"{self.base_url}/stream?streams={'/'.join(streams)}"
            
            logging.info(f"🔗 Connecting to Binance WebSocket (attempt {self.connection_attempts + 1})")
            logging.info(f"📡 Monitoring: {', '.join(list(self.monitored_symbols)[:5])}{'...' if len(self.monitored_symbols) > 5 else ''}")
            
            # Create WebSocket with enhanced callbacks
            self.ws = websocket.WebSocketApp(
                stream_url,
                on_message=self._on_message,
                on_error=self._on_error,
                on_close=self._on_close,
                on_open=self._on_open,
                on_ping=self._on_ping,
                on_pong=self._on_pong
            )
            
            # Start connection in background thread
            self.ws_thread = threading.Thread(target=self._run_websocket, daemon=True)
            self.ws_thread.start()
            
            # Start health monitoring
            self.health_thread = threading.Thread(target=self._health_monitor, daemon=True)
            self.health_thread.start()
            
            return True
            
        except Exception as e:
            logging.error(f"❌ Connection failed: {e}")
            self.connection_attempts += 1
            return False
    
    def _run_websocket(self):
        """Run WebSocket with 2025 Binance requirements"""
        try:
            # Run with proper ping/pong handling
            self.ws.run_forever(
                ping_interval=None,  # Don't send our own pings, respond to Binance pings
                ping_timeout=self.pong_timeout,
                ping_payload=b'',  # Empty payload as recommended
                skip_utf8_validation=False
            )
        except Exception as e:
            logging.error(f"❌ WebSocket runtime error: {e}")
            self.is_connected = False
    
    def _on_open(self, ws):
        """Enhanced connection opened handler"""
        self.is_connected = True
        self.connection_start_time = datetime.now()
        self.last_message_time = datetime.now()
        self.connection_attempts = 0  # Reset on successful connection
        
        logging.info("✅ Binance WebSocket connected successfully")
        logging.info(f"📊 Monitoring {len(self.monitored_symbols)} symbols")
        
        # Notify callbacks
        for callback in self.callbacks:
            try:
                callback('connection', 'connected', {'status': 'connected', 'symbols': len(self.monitored_symbols)})
            except Exception as e:
                logging.error(f"❌ Callback error: {e}")
    
    def _on_ping(self, ws, message):
        """Handle ping from Binance server (2025 requirement)"""
        self.last_ping_time = datetime.now()
        logging.debug(f"📡 Received ping from Binance: {len(message)} bytes")
        
        # Binance requires immediate pong response
        try:
            ws.pong(message)
            logging.debug("📤 Sent pong response")
        except Exception as e:
            logging.error(f"❌ Failed to send pong: {e}")
    
    def _on_pong(self, ws, message):
        """Handle pong response"""
        self.last_pong_time = datetime.now()
        logging.debug("📥 Received pong response")
    
    def _on_message(self, ws, message):
        """Enhanced message processing with 2025 data structures"""
        try:
            self.last_message_time = datetime.now()
            data = json.loads(message)
            
            # Handle combined stream format
            if 'stream' in data and 'data' in data:
                stream = data['stream']
                stream_data = data['data']
                
                if '@ticker' in stream:
                    self._process_ticker_data(stream_data)
                else:
                    logging.debug(f"📊 Unhandled stream type: {stream}")
            else:
                logging.debug(f"📊 Non-stream message: {data}")
                
        except json.JSONDecodeError as e:
            logging.error(f"❌ JSON decode error: {e}")
        except Exception as e:
            logging.error(f"❌ Message processing error: {e}")
    
    def _process_ticker_data(self, data):
        """Process 24hr ticker data with 2025 enhancements"""
        try:
            symbol = data['s']
            price = float(data['c'])
            price_change_pct = float(data['P'])
            volume = float(data['v'])
            
            # Store current data
            self.price_data[symbol] = {
                'price': price,
                'price_change_24h': price_change_pct,
                'volume_24h': volume,
                'timestamp': datetime.now(),
                'high_24h': float(data['h']),
                'low_24h': float(data['l']),
                'open_24h': float(data['o']),
                'count': int(data['n'])  # Number of trades
            }
            
            # Check for unlock indicators (significant price drops)
            if price_change_pct < -10:  # 10% drop
                self._trigger_unlock_alert(symbol, data)
            
            # Notify callbacks
            for callback in self.callbacks:
                try:
                    callback('ticker', symbol, self.price_data[symbol])
                except Exception as e:
                    logging.error(f"❌ Callback error for {symbol}: {e}")
                    
        except Exception as e:
            logging.error(f"❌ Ticker processing error: {e}")
    
    def _trigger_unlock_alert(self, symbol, data):
        """Trigger unlock event alert"""
        alert_data = {
            'symbol': symbol,
            'price_change_24h': float(data['P']),
            'current_price': float(data['c']),
            'volume_24h': float(data['v']),
            'alert_type': 'significant_price_drop',
            'confidence': 'medium' if float(data['P']) < -15 else 'low',
            'timestamp': datetime.now().isoformat()
        }
        
        logging.warning(f"🚨 UNLOCK ALERT: {symbol} dropped {data['P']:.2f}% in 24h")
        
        for callback in self.callbacks:
            try:
                callback('unlock_alert', symbol, alert_data)
            except Exception as e:
                logging.error(f"❌ Alert callback error: {e}")
    
    def _on_error(self, ws, error):
        """Enhanced error handling for 2025"""
        logging.error(f"❌ Binance WebSocket error: {error}")
        self.is_connected = False
        
        # Categorize errors for better handling
        error_str = str(error).lower()
        if "1006" in error_str:
            logging.error("🔌 Connection closed abnormally (1006) - likely network issue")
        elif "timeout" in error_str:
            logging.error("⏰ Connection timeout - will attempt reconnection")
        elif "ssl" in error_str:
            logging.error("🔒 SSL error - check certificates")
        elif "rate" in error_str or "limit" in error_str:
            logging.error("🚫 Rate limit exceeded - backing off")
            time.sleep(60)  # Wait longer for rate limit issues
        else:
            logging.error(f"🔍 Unknown error type: {type(error).__name__}")
    
    def _on_close(self, ws, close_status_code, close_msg):
        """Enhanced close handling with 2025 reconnection logic"""
        self.is_connected = False
        
        if close_status_code:
            logging.warning(f"❌ WebSocket closed: {close_status_code} - {close_msg}")
        else:
            logging.warning(f"❌ WebSocket closed unexpectedly: {close_msg}")
        
        # Check if we should reconnect
        if self.monitored_symbols and close_status_code != 1000:  # 1000 = normal closure
            self._schedule_reconnection()
        else:
            logging.info("ℹ️ WebSocket closed normally or no symbols to monitor")
    
    def _schedule_reconnection(self):
        """Schedule reconnection with progressive backoff"""
        if self.connection_attempts < self.max_connection_attempts:
            delay_index = min(self.connection_attempts, len(self.reconnect_delays) - 1)
            delay = self.reconnect_delays[delay_index]
            
            logging.info(f"🔄 Scheduling reconnection in {delay}s (attempt {self.connection_attempts + 1}/{self.max_connection_attempts})")
            
            def reconnect():
                time.sleep(delay)
                self.connection_attempts += 1
                self._connect()
            
            threading.Thread(target=reconnect, daemon=True).start()
        else:
            logging.error(f"❌ Max reconnection attempts reached. Stopping.")
    
    def _health_monitor(self):
        """Monitor connection health and force reconnection if needed"""
        while True:
            try:
                time.sleep(self.health_check_interval)
                
                if not self.is_connected:
                    continue
                
                now = datetime.now()
                
                # Check if connection is too old (24 hour limit)
                if self.connection_start_time and (now - self.connection_start_time).total_seconds() > self.connection_timeout:
                    logging.warning("⏰ Connection approaching 24-hour limit, reconnecting...")
                    self.stop_monitoring()
                    time.sleep(5)
                    self.start_monitoring()
                    continue
                
                # Check if we're receiving messages
                if self.last_message_time and (now - self.last_message_time).total_seconds() > 300:  # 5 minutes
                    logging.warning("📡 No messages received for 5 minutes, connection may be stale")
                    self.stop_monitoring()
                    time.sleep(5)
                    self.start_monitoring()
                
            except Exception as e:
                logging.error(f"❌ Health monitor error: {e}")
    
    def get_current_prices(self) -> Dict[str, Dict]:
        """Get current price data for all monitored symbols"""
        return self.price_data.copy()
    
    def get_status(self) -> Dict[str, any]:
        """Get connection status and statistics"""
        return {
            'connected': self.is_connected,
            'symbols_monitored': len(self.monitored_symbols),
            'connection_attempts': self.connection_attempts,
            'connection_start_time': self.connection_start_time.isoformat() if self.connection_start_time else None,
            'last_message_time': self.last_message_time.isoformat() if self.last_message_time else None,
            'price_data_count': len(self.price_data)
        }
    
    def add_callback(self, callback: Callable):
        """Add callback for events"""
        self.callbacks.append(callback)
    
    def stop_monitoring(self):
        """Stop monitoring and close connection"""
        logging.info("🛑 Stopping Binance WebSocket monitoring")
        self.is_connected = False
        if self.ws:
            self.ws.close()
        self.ws = None

# Global instance for easy access
binance_monitor_2025 = BinanceWebSocket2025()

def start_monitoring_2025(symbols: List[str], callback: Callable = None) -> BinanceWebSocket2025:
    """Start monitoring with 2025 enhanced features"""
    monitor = BinanceWebSocket2025()
    monitor.add_symbols(symbols)
    if callback:
        monitor.add_callback(callback)
    monitor.start_monitoring()
    return monitor

if __name__ == "__main__":
    # Test the 2025 implementation
    def test_callback(event_type, symbol, data):
        if event_type == 'unlock_alert':
            print(f"🚨 UNLOCK ALERT: {symbol} - {data}")
        elif event_type == 'ticker':
            print(f"📊 {symbol}: ${data['price']:.4f} ({data['price_change_24h']:+.2f}%)")
        elif event_type == 'connection':
            print(f"🔗 Connection: {data}")
    
    # Start monitoring major DeFi tokens
    symbols = ['BTC', 'ETH', 'UNI', 'AAVE', 'LINK', 'COMP']
    monitor = start_monitoring_2025(symbols, test_callback)
    
    try:
        while True:
            time.sleep(10)
            status = monitor.get_status()
            print(f"📊 Status: Connected={status['connected']}, Symbols={status['symbols_monitored']}, Data={status['price_data_count']}")
    except KeyboardInterrupt:
        print("\n🛑 Stopping monitor...")
        monitor.stop_monitoring()
