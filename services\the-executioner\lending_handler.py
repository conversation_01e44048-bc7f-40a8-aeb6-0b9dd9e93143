import logging
from decimal import Decimal
from web3 import Web3
from eth_account import Account

# Aave V3 Pool contract address on Ethereum Mainnet
AAVE_V3_POOL_ADDRESS = "******************************************"

# Simplified Aave V3 Pool ABI (only functions we need)
AAVE_V3_POOL_ABI = [
    {
        "inputs": [
            {"internalType": "address", "name": "asset", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"},
            {"internalType": "uint256", "name": "interestRateMode", "type": "uint256"},
            {"internalType": "uint16", "name": "referralCode", "type": "uint16"},
            {"internalType": "address", "name": "onBehalfOf", "type": "address"}
        ],
        "name": "borrow",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "asset", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"},
            {"internalType": "uint256", "name": "rateMode", "type": "uint256"},
            {"internalType": "address", "name": "onBehalfOf", "type": "address"}
        ],
        "name": "repay",
        "outputs": [{"internalType": "uint256", "name": "", "type": "uint256"}],
        "stateMutability": "nonpayable",
        "type": "function"
    },
    {
        "inputs": [
            {"internalType": "address", "name": "asset", "type": "address"},
            {"internalType": "uint256", "name": "amount", "type": "uint256"},
            {"internalType": "address", "name": "onBehalfOf", "type": "address"},
            {"internalType": "uint16", "name": "referralCode", "type": "uint16"}
        ],
        "name": "supply",
        "outputs": [],
        "stateMutability": "nonpayable",
        "type": "function"
    }
]

def borrow_asset(web3: Web3, account: Account, asset_address: str, amount: Decimal) -> str:
    """
    Borrows an asset from Aave V3
    
    Args:
        web3: Web3 instance
        account: Account to borrow with
        asset_address: Address of the asset to borrow
        amount: Amount to borrow (in token units, not wei)
    
    Returns:
        Transaction hash if successful, None if failed
    """
    try:
        # Get the Aave V3 Pool contract
        pool_contract = web3.eth.contract(
            address=AAVE_V3_POOL_ADDRESS,
            abi=AAVE_V3_POOL_ABI
        )
        
        # Get token decimals to convert amount to wei
        decimals = get_token_decimals(web3, asset_address)
        amount_wei = int(amount * (10 ** decimals))
        
        logging.info(f"Borrowing {amount} tokens ({amount_wei} wei) of {asset_address}")
        
        # Prepare borrow transaction
        # interestRateMode: 1 = stable, 2 = variable (we use variable)
        borrow_tx = pool_contract.functions.borrow(
            asset_address,           # asset
            amount_wei,             # amount
            2,                      # interestRateMode (variable)
            0,                      # referralCode
            account.address         # onBehalfOf
        )
        
        # Estimate gas
        gas_estimate = borrow_tx.estimate_gas({'from': account.address})
        gas_price = web3.eth.gas_price
        
        # Build transaction
        transaction = borrow_tx.build_transaction({
            'from': account.address,
            'gas': int(gas_estimate * 1.2),  # Add 20% buffer
            'gasPrice': gas_price,
            'nonce': web3.eth.get_transaction_count(account.address)
        })
        
        # Sign and send transaction
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            logging.info(f"Borrow successful. Tx hash: {tx_hash.hex()}")
            return tx_hash.hex()
        else:
            logging.error(f"Borrow transaction failed. Tx hash: {tx_hash.hex()}")
            return None
            
    except Exception as e:
        logging.error(f"Error borrowing asset {asset_address}: {e}")
        return None

def repay_asset(web3: Web3, account: Account, asset_address: str, amount: Decimal) -> str:
    """
    Repays a borrowed asset to Aave V3
    
    Args:
        web3: Web3 instance
        account: Account to repay with
        asset_address: Address of the asset to repay
        amount: Amount to repay (in token units, not wei)
    
    Returns:
        Transaction hash if successful, None if failed
    """
    try:
        # Get the Aave V3 Pool contract
        pool_contract = web3.eth.contract(
            address=AAVE_V3_POOL_ADDRESS,
            abi=AAVE_V3_POOL_ABI
        )
        
        # Get token decimals to convert amount to wei
        decimals = get_token_decimals(web3, asset_address)
        amount_wei = int(amount * (10 ** decimals))
        
        logging.info(f"Repaying {amount} tokens ({amount_wei} wei) of {asset_address}")
        
        # First, approve the pool to spend our tokens
        approve_tx_hash = approve_token_spending(web3, account, asset_address, AAVE_V3_POOL_ADDRESS, amount_wei)
        if not approve_tx_hash:
            raise Exception("Failed to approve token spending")
        
        # Prepare repay transaction
        repay_tx = pool_contract.functions.repay(
            asset_address,           # asset
            amount_wei,             # amount
            2,                      # rateMode (variable)
            account.address         # onBehalfOf
        )
        
        # Estimate gas
        gas_estimate = repay_tx.estimate_gas({'from': account.address})
        gas_price = web3.eth.gas_price
        
        # Build transaction
        transaction = repay_tx.build_transaction({
            'from': account.address,
            'gas': int(gas_estimate * 1.2),  # Add 20% buffer
            'gasPrice': gas_price,
            'nonce': web3.eth.get_transaction_count(account.address)
        })
        
        # Sign and send transaction
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            logging.info(f"Repay successful. Tx hash: {tx_hash.hex()}")
            return tx_hash.hex()
        else:
            logging.error(f"Repay transaction failed. Tx hash: {tx_hash.hex()}")
            return None
            
    except Exception as e:
        logging.error(f"Error repaying asset {asset_address}: {e}")
        return None

def get_token_decimals(web3: Web3, token_address: str) -> int:
    """Get the decimals of an ERC20 token"""
    try:
        erc20_abi = [
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        
        contract = web3.eth.contract(address=token_address, abi=erc20_abi)
        decimals = contract.functions.decimals().call()
        return decimals
        
    except Exception as e:
        logging.error(f"Error getting decimals for {token_address}: {e}")
        return 18  # Default to 18 decimals

def approve_token_spending(web3: Web3, account: Account, token_address: str, spender: str, amount: int) -> str:
    """Approve a spender to spend tokens on behalf of the account"""
    try:
        erc20_abi = [
            {
                "constant": False,
                "inputs": [
                    {"name": "_spender", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "approve",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            }
        ]
        
        contract = web3.eth.contract(address=token_address, abi=erc20_abi)
        
        # Build approve transaction
        approve_tx = contract.functions.approve(spender, amount)
        
        gas_estimate = approve_tx.estimate_gas({'from': account.address})
        gas_price = web3.eth.gas_price
        
        transaction = approve_tx.build_transaction({
            'from': account.address,
            'gas': int(gas_estimate * 1.2),
            'gasPrice': gas_price,
            'nonce': web3.eth.get_transaction_count(account.address)
        })
        
        # Sign and send
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            logging.info(f"Token approval successful. Tx hash: {tx_hash.hex()}")
            return tx_hash.hex()
        else:
            logging.error(f"Token approval failed. Tx hash: {tx_hash.hex()}")
            return None
            
    except Exception as e:
        logging.error(f"Error approving token spending: {e}")
        return None
