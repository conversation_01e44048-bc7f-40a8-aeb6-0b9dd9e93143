#!/usr/bin/env python3
"""
Simplified Live Paper Trading for Project Chimera
Focuses on core functionality without external API dependencies
"""

import os
import sys
import time
import json
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import random

# Set paper trading mode and environment
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['MONITORING_INTERVAL_SECONDS'] = '15'  # Check every 15 seconds for demo

# Set default configuration
os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.75')
os.environ.setdefault('STOP_LOSS_PCT', '0.15')
os.environ.setdefault('TAKE_PROFIT_PCT', '0.10')
os.environ.setdefault('BORROW_AMOUNT_PER_TRADE', '1000')
os.environ.setdefault('TAKE_PROFIT_DAYS_BEFORE_UNLOCK', '1')

# Mock Redis URL for local testing
if not os.environ.get('REDIS_URL'):
    os.environ['REDIS_URL'] = 'redis://localhost:6379'

# Add service paths
for service in ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']:
    sys.path.insert(0, str(Path(__file__).parent / 'services' / service))

class SimplePaperTradingRunner:
    """Simplified paper trading runner with realistic market simulation"""
    
    def __init__(self):
        self.running = False
        self.positions = []
        self.monitoring_thread = None
        self.session_start_time = datetime.now()
        
    def start_paper_trading_session(self):
        """Start a complete paper trading session"""
        print("🚀 Project Chimera - Simplified Live Paper Trading")
        print("=" * 60)
        print("🧪 Running with SIMULATED market data in SAFE paper mode")
        print("💡 No real money at risk - all trades are simulated")
        print("🎯 Focus: Testing core trading logic and risk management")
        
        # Initialize paper trading engine
        if not self.initialize_paper_engine():
            return
        
        # Create realistic demo scenarios
        print("\n🔮 Step 1: Creating Realistic Market Scenarios...")
        unlock_events = self.create_realistic_scenarios()
        
        # Analyze scenarios
        print("\n🧠 Step 2: Running Strategy Analysis...")
        trade_candidates = self.analyze_scenarios(unlock_events)
        
        # Execute trades
        print("\n⚔️ Step 3: Executing Paper Trades...")
        self.execute_paper_trades(trade_candidates)
        
        # Start monitoring
        print("\n📊 Step 4: Starting Real-Time Risk Monitoring...")
        self.start_monitoring()
        
        # Run session
        self.run_session()
        
    def initialize_paper_engine(self):
        """Initialize the paper trading engine"""
        try:
            from paper_trading import paper_engine
            self.paper_engine = paper_engine
            
            # Get initial portfolio
            portfolio = self.paper_engine.get_portfolio_summary()
            print(f"💰 Initial Portfolio: {portfolio['balances']}")
            print(f"📊 Starting Performance: {portfolio['performance']}")
            
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize paper engine: {e}")
            return False
    
    def create_realistic_scenarios(self):
        """Create realistic unlock scenarios based on real market patterns"""
        
        # Generate future dates
        scenarios = [
            {
                'token_symbol': 'UNI',
                'contract_address': '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
                'unlock_date': (datetime.now() + timedelta(days=25)).isoformat() + 'Z',
                'unlock_amount': 75000000,  # 75M tokens
                'circulating_supply': 750000000,  # 750M circulating
                'total_supply': 1000000000,  # 1B total
                'volume_24h': 150000000,  # $150M daily volume
                'source': 'Realistic_Simulation',
                'current_price': 8.50  # Starting price for simulation
            },
            {
                'token_symbol': 'COMP',
                'contract_address': '0xc00e94Cb662C3520282E6f5717214004A7f26888',
                'unlock_date': (datetime.now() + timedelta(days=35)).isoformat() + 'Z',
                'unlock_amount': 25000000,  # 25M tokens
                'circulating_supply': 100000000,  # 100M circulating
                'total_supply': 10000000,  # 10M total (note: COMP has complex tokenomics)
                'volume_24h': 80000000,  # $80M daily volume
                'source': 'Realistic_Simulation',
                'current_price': 65.00  # Starting price for simulation
            },
            {
                'token_symbol': 'AAVE',
                'contract_address': '0x7Fc66500c84A76Ad7e9c93437bFc5Ac33E2DDaE9',
                'unlock_date': (datetime.now() + timedelta(days=45)).isoformat() + 'Z',
                'unlock_amount': 5000000,  # 5M tokens
                'circulating_supply': 14000000,  # 14M circulating
                'total_supply': 16000000,  # 16M total
                'volume_24h': 120000000,  # $120M daily volume
                'source': 'Realistic_Simulation',
                'current_price': 180.00  # Starting price for simulation
            }
        ]
        
        from datetime import timezone

        print(f"📅 Created {len(scenarios)} realistic unlock scenarios:")
        for scenario in scenarios:
            unlock_dt = datetime.fromisoformat(scenario['unlock_date'].replace('Z', '+00:00'))
            now_dt = datetime.now(timezone.utc)
            days_to_unlock = (unlock_dt - now_dt).days
            print(f"   🪙 {scenario['token_symbol']}: ${scenario['current_price']:.2f}, unlock in {days_to_unlock} days")
            print(f"      📊 {scenario['unlock_amount']:,} tokens ({scenario['unlock_amount']/scenario['circulating_supply']*100:.1f}% of supply)")
        
        return scenarios
    
    def analyze_scenarios(self, scenarios):
        """Analyze scenarios using the Seer's pressure score algorithm"""
        try:
            from analysis import calculate_unlock_pressure_score
            
            trade_candidates = []
            threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
            
            print(f"🎯 Analyzing scenarios with threshold: {threshold}")
            
            for scenario in scenarios:
                print(f"\n🔍 Analyzing {scenario['token_symbol']}...")
                
                # Calculate pressure score
                score = calculate_unlock_pressure_score(scenario)
                
                # Calculate additional metrics
                unlock_pct = (scenario['unlock_amount'] / scenario['circulating_supply']) * 100
                volume_ratio = scenario['unlock_amount'] / (scenario['volume_24h'] / scenario['current_price'])
                
                print(f"   📊 Pressure Score: {score:.4f}")
                print(f"   📈 Unlock Impact: {unlock_pct:.1f}% of circulating supply")
                print(f"   💧 Volume Ratio: {volume_ratio:.2f}x daily volume")
                
                if score > threshold:
                    candidate = {**scenario, 'pressure_score': score}
                    trade_candidates.append(candidate)
                    print(f"   ✅ TRADE CANDIDATE: High conviction (score: {score:.4f})")
                else:
                    print(f"   ❌ Below threshold: {score:.4f} < {threshold}")
            
            print(f"\n🎯 Strategy Analysis Complete: {len(trade_candidates)} candidates identified")
            return trade_candidates
            
        except Exception as e:
            print(f"❌ Analysis error: {e}")
            return []
    
    def execute_paper_trades(self, candidates):
        """Execute paper trades for qualified candidates"""
        try:
            from paper_trading import execute_paper_trade
            
            print(f"💼 Executing trades for {len(candidates)} candidates...")
            
            for candidate in candidates:
                print(f"\n📈 Opening position: {candidate['token_symbol']}")
                
                try:
                    # Execute paper trade
                    position = execute_paper_trade(candidate)
                    
                    # Add current price for monitoring
                    position['current_market_price'] = candidate['current_price']
                    position['price_volatility'] = random.uniform(0.02, 0.08)  # 2-8% daily volatility
                    
                    self.positions.append(position)
                    
                    print(f"   ✅ Position opened: ID {position['position_id']}")
                    print(f"   💰 Entry price: ${position['entry_price_in_usdc']:.4f}")
                    print(f"   📊 Market price: ${candidate['current_price']:.2f}")
                    
                except Exception as e:
                    print(f"   ❌ Failed to open position: {e}")
            
            print(f"\n📊 Trade Execution Complete: {len(self.positions)} positions opened")
            
        except Exception as e:
            print(f"❌ Execution error: {e}")
    
    def start_monitoring(self):
        """Start position monitoring in background thread"""
        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitor_positions)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        print("🔄 Real-time monitoring started")
    
    def monitor_positions(self):
        """Monitor positions with realistic price simulation"""
        try:
            from risk_manager import check_risk_rules
            
            while self.running:
                open_positions = [p for p in self.positions if p.get('status') == 'OPEN']
                
                if not open_positions:
                    time.sleep(2)
                    continue
                
                print(f"\n📊 Monitoring {len(open_positions)} positions...")
                
                for position in open_positions:
                    try:
                        # Simulate realistic price movement
                        current_price = self.simulate_price_movement(position)
                        
                        # Check risk rules
                        action, reason = check_risk_rules(position, current_price)
                        
                        # Calculate P&L
                        entry_price = Decimal(str(position['entry_price_in_usdc']))
                        pnl_pct = ((entry_price - current_price) / entry_price) * 100  # For shorts
                        
                        print(f"   📈 {position['token_symbol']}: ${current_price:.4f} (P&L: {pnl_pct:+.2f}%) - {action}")
                        
                        if action == "CLOSE":
                            print(f"   🚨 CLOSING: {reason}")
                            self.close_position(position, current_price, reason)
                    
                    except Exception as e:
                        print(f"   ❌ Error monitoring {position.get('token_symbol', 'Unknown')}: {e}")
                
                time.sleep(int(os.environ.get('MONITORING_INTERVAL_SECONDS', '15')))
                
        except Exception as e:
            print(f"❌ Monitoring error: {e}")
    
    def simulate_price_movement(self, position):
        """Simulate realistic price movement for a position"""
        # Get base market price
        base_price = position.get('current_market_price', position['entry_price_in_usdc'])
        volatility = position.get('price_volatility', 0.05)
        
        # Simulate random walk with slight downward bias (unlock decay effect)
        time_elapsed = (datetime.now() - self.session_start_time).total_seconds() / 3600  # hours
        
        # Add slight downward trend as we approach unlock (simulating unlock decay)
        trend_factor = -0.001 * time_elapsed  # -0.1% per hour
        
        # Add random volatility
        random_factor = random.gauss(0, volatility / 24)  # Scale volatility to hourly
        
        # Calculate new price
        price_change = trend_factor + random_factor
        new_price = Decimal(str(base_price)) * (1 + Decimal(str(price_change)))
        
        # Update position's market price
        position['current_market_price'] = float(new_price)
        
        return new_price
    
    def close_position(self, position, current_price, reason):
        """Close a position"""
        try:
            from paper_trading import close_paper_position
            
            closed_position = close_paper_position(
                position['position_id'], 
                reason, 
                current_price
            )
            
            position['status'] = 'CLOSED'
            pnl = closed_position.get('pnl_usd', 0)
            print(f"   ✅ Position closed: P&L ${pnl:.2f}")
            
        except Exception as e:
            print(f"   ❌ Error closing position: {e}")
    
    def run_session(self):
        """Run the trading session"""
        duration_minutes = 5  # 5-minute demo session
        
        print(f"\n⏰ Running {duration_minutes}-minute live paper trading session...")
        print("📊 Press Ctrl+C to stop early and see results")
        
        try:
            for minute in range(duration_minutes):
                print(f"\n⏱️ Minute {minute + 1}/{duration_minutes}")
                
                # Show portfolio summary
                portfolio = self.paper_engine.get_portfolio_summary()
                print(f"💰 Balances: {portfolio['balances']}")
                print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
                
                open_count = len([p for p in self.positions if p.get('status') == 'OPEN'])
                closed_count = len([p for p in self.positions if p.get('status') == 'CLOSED'])
                print(f"📊 Positions: {open_count} open, {closed_count} closed")
                
                time.sleep(60)  # Wait 1 minute
                
        except KeyboardInterrupt:
            print("\n⏹️ Session stopped by user")
        
        finally:
            self.stop_session()
    
    def stop_session(self):
        """Stop the session and show final results"""
        print("\n🛑 Stopping paper trading session...")
        
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=3)
        
        # Final summary
        portfolio = self.paper_engine.get_portfolio_summary()
        
        print("\n" + "=" * 60)
        print("📋 LIVE PAPER TRADING SESSION SUMMARY")
        print("=" * 60)
        print(f"⏱️ Session Duration: {(datetime.now() - self.session_start_time).total_seconds()/60:.1f} minutes")
        print(f"💰 Final Balances: {portfolio['balances']}")
        print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
        print(f"📊 Total Trades: {portfolio['performance']['total_trades']}")
        
        if portfolio['performance']['total_trades'] > 0:
            win_rate = portfolio['performance']['winning_trades'] / portfolio['performance']['total_trades'] * 100
            print(f"🎯 Win Rate: {win_rate:.1f}% ({portfolio['performance']['winning_trades']}/{portfolio['performance']['total_trades']})")
        
        # Save session log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"simple_paper_trading_session_{timestamp}.json"
        
        session_data = {
            'session_summary': portfolio,
            'positions': self.positions,
            'session_duration_minutes': (datetime.now() - self.session_start_time).total_seconds()/60,
            'timestamp': timestamp
        }
        
        with open(log_file, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        print(f"📄 Session log saved: {log_file}")
        print("\n✅ Live paper trading session completed successfully!")
        print("🎯 System is ready for extended testing or live deployment")

def main():
    """Main entry point"""
    runner = SimplePaperTradingRunner()
    runner.start_paper_trading_session()

if __name__ == "__main__":
    main()
