"""
Common utilities and shared components for Project Chimera services.

This package contains shared functionality used across all microservices:
- Error handling and resilience patterns
- Logging utilities
- Configuration management
- Health checking
- Rate limiting and circuit breakers
"""

from .error_handling import (
    ChimeraError,
    DataSourceError,
    TradingError,
    RiskManagementError,
    CircuitBreakerError,
    CircuitBreaker,
    retry_with_backoff,
    RateLimiter,
    safe_api_call,
    log_error_with_context,
    HealthChecker,
    default_circuit_breaker,
    api_rate_limiter,
    health_checker
)

__all__ = [
    'ChimeraError',
    'DataSourceError', 
    'TradingError',
    'RiskManagementError',
    'CircuitBreakerError',
    'CircuitBreaker',
    'retry_with_backoff',
    'RateLimiter',
    'safe_api_call',
    'log_error_with_context',
    'HealthChecker',
    'default_circuit_breaker',
    'api_rate_limiter',
    'health_checker'
]
