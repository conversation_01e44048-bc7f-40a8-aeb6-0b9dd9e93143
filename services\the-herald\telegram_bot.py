import os
import requests
import logging
from typing import Optional

# Secrets loaded from environment variables
TELEGRAM_BOT_TOKEN = os.environ.get("TELEGRAM_BOT_TOKEN")
TELEGRAM_CHAT_ID = os.environ.get("TELEGRAM_CHAT_ID")

# Telegram API configuration
TELEGRAM_API_BASE = "https://api.telegram.org"
REQUEST_TIMEOUT = 30
MAX_MESSAGE_LENGTH = 4096  # Telegram's message length limit

def send_telegram_message(text: str, parse_mode: str = "Markdown") -> bool:
    """
    Sends a message to the configured Telegram chat.
    
    Args:
        text: Message text to send
        parse_mode: Parsing mode for the message (Markdown, HTML, or None)
    
    Returns:
        True if message was sent successfully, False otherwise
    """
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        logging.warning("Telegram secrets not set. Skipping notification.")
        print(f"TELEGRAM MESSAGE (would send): {text}")
        return False

    try:
        # Split long messages if necessary
        messages = split_long_message(text)
        
        for message_part in messages:
            success = _send_single_message(message_part, parse_mode)
            if not success:
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"Unexpected error sending Telegram message: {e}")
        return False

def _send_single_message(text: str, parse_mode: str) -> bool:
    """Send a single message to Telegram"""
    url = f"{TELEGRAM_API_BASE}/bot{TELEGRAM_BOT_TOKEN}/sendMessage"
    
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "text": text,
        "parse_mode": parse_mode,
        "disable_web_page_preview": True  # Disable link previews for cleaner messages
    }
    
    try:
        response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            logging.info(f"Telegram message sent successfully: {text[:50]}...")
            return True
        else:
            logging.error(f"Telegram API error: {result.get('description', 'Unknown error')}")
            return False
            
    except requests.RequestException as e:
        logging.error(f"Error sending Telegram message: {e}")
        return False

def split_long_message(text: str) -> list:
    """
    Split a long message into multiple parts if it exceeds Telegram's limit
    """
    if len(text) <= MAX_MESSAGE_LENGTH:
        return [text]
    
    messages = []
    current_message = ""
    lines = text.split('\n')
    
    for line in lines:
        # If adding this line would exceed the limit, start a new message
        if len(current_message) + len(line) + 1 > MAX_MESSAGE_LENGTH:
            if current_message:
                messages.append(current_message.strip())
                current_message = line
            else:
                # Single line is too long, split it
                while len(line) > MAX_MESSAGE_LENGTH:
                    messages.append(line[:MAX_MESSAGE_LENGTH])
                    line = line[MAX_MESSAGE_LENGTH:]
                current_message = line
        else:
            if current_message:
                current_message += '\n' + line
            else:
                current_message = line
    
    if current_message:
        messages.append(current_message.strip())
    
    return messages

def send_photo(photo_url: str, caption: str = "") -> bool:
    """
    Send a photo to the Telegram chat
    
    Args:
        photo_url: URL of the photo to send
        caption: Optional caption for the photo
    
    Returns:
        True if photo was sent successfully, False otherwise
    """
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        logging.warning("Telegram secrets not set. Skipping photo.")
        return False

    url = f"{TELEGRAM_API_BASE}/bot{TELEGRAM_BOT_TOKEN}/sendPhoto"
    
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "photo": photo_url,
        "caption": caption,
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            logging.info(f"Telegram photo sent successfully")
            return True
        else:
            logging.error(f"Telegram API error sending photo: {result.get('description', 'Unknown error')}")
            return False
            
    except requests.RequestException as e:
        logging.error(f"Error sending Telegram photo: {e}")
        return False

def send_document(document_url: str, caption: str = "") -> bool:
    """
    Send a document to the Telegram chat
    
    Args:
        document_url: URL of the document to send
        caption: Optional caption for the document
    
    Returns:
        True if document was sent successfully, False otherwise
    """
    if not TELEGRAM_BOT_TOKEN or not TELEGRAM_CHAT_ID:
        logging.warning("Telegram secrets not set. Skipping document.")
        return False

    url = f"{TELEGRAM_API_BASE}/bot{TELEGRAM_BOT_TOKEN}/sendDocument"
    
    payload = {
        "chat_id": TELEGRAM_CHAT_ID,
        "document": document_url,
        "caption": caption,
        "parse_mode": "Markdown"
    }
    
    try:
        response = requests.post(url, json=payload, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            logging.info(f"Telegram document sent successfully")
            return True
        else:
            logging.error(f"Telegram API error sending document: {result.get('description', 'Unknown error')}")
            return False
            
    except requests.RequestException as e:
        logging.error(f"Error sending Telegram document: {e}")
        return False

def test_telegram_connection() -> bool:
    """
    Test the Telegram bot connection by sending a test message
    
    Returns:
        True if connection is working, False otherwise
    """
    test_message = "🧪 **Telegram Connection Test** 🧪\n✅ Bot is working correctly!"
    return send_telegram_message(test_message)

def get_bot_info() -> Optional[dict]:
    """
    Get information about the Telegram bot
    
    Returns:
        Bot information dict if successful, None otherwise
    """
    if not TELEGRAM_BOT_TOKEN:
        logging.warning("Telegram bot token not set.")
        return None

    url = f"{TELEGRAM_API_BASE}/bot{TELEGRAM_BOT_TOKEN}/getMe"
    
    try:
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        result = response.json()
        if result.get("ok"):
            return result.get("result")
        else:
            logging.error(f"Telegram API error getting bot info: {result.get('description', 'Unknown error')}")
            return None
            
    except requests.RequestException as e:
        logging.error(f"Error getting Telegram bot info: {e}")
        return None
