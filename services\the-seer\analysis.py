import requests
import logging
from typing import Dict, Any
from decimal import Decimal

def calculate_unlock_pressure_score(event: Dict[str, Any]) -> float:
    """
    Calculates the Unlock Pressure Score.
    A simple but effective formula based on the project hypothesis.
    (Unlock Amount / Current Circulating Supply) * (Unlock Amount / 24h Trading Volume)
    """
    try:
        # Get real-time data for more accurate scoring
        token_symbol = event.get("token_symbol")
        contract_address = event.get("contract_address")
        unlock_amount = float(event.get("unlock_amount", 0.0))
        
        # Try to fetch real-time data from CoinGecko
        market_data = fetch_market_data(token_symbol, contract_address)
        
        if market_data:
            circulating_supply = market_data.get("circulating_supply", event.get("circulating_supply", 500000000.0))
            volume_24h = market_data.get("volume_24h", 100000000.0)
        else:
            # Fallback to event data or mock data
            circulating_supply = event.get("circulating_supply", 500000000.0)
            volume_24h = event.get("volume_24h", 100000000.0)
        
        if circulating_supply == 0 or volume_24h == 0:
            logging.warning(f"Invalid market data for {token_symbol}. Using fallback calculation.")
            return 0.0

        size_impact = unlock_amount / circulating_supply
        liquidity_impact = unlock_amount / volume_24h

        score = size_impact * liquidity_impact
        
        logging.info(f"Pressure Score calculation for {token_symbol}:")
        logging.info(f"  Unlock Amount: {unlock_amount:,.0f}")
        logging.info(f"  Circulating Supply: {circulating_supply:,.0f}")
        logging.info(f"  24h Volume: {volume_24h:,.0f}")
        logging.info(f"  Size Impact: {size_impact:.4f}")
        logging.info(f"  Liquidity Impact: {liquidity_impact:.4f}")
        logging.info(f"  Final Score: {score:.4f}")
        
        return score
        
    except Exception as e:
        logging.error(f"Error calculating pressure score: {e}")
        return 0.0

def fetch_market_data(token_symbol: str, contract_address: str) -> Dict[str, Any]:
    """
    Fetches real-time market data from CoinGecko API
    """
    try:
        # CoinGecko free API endpoint
        url = f"https://api.coingecko.com/api/v3/coins/ethereum/contract/{contract_address}"
        
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            data = response.json()
            market_data = data.get("market_data", {})
            
            return {
                "circulating_supply": market_data.get("circulating_supply"),
                "total_supply": market_data.get("total_supply"),
                "volume_24h": market_data.get("total_volume", {}).get("usd"),
                "current_price": market_data.get("current_price", {}).get("usd"),
                "market_cap": market_data.get("market_cap", {}).get("usd")
            }
        else:
            logging.warning(f"CoinGecko API returned status {response.status_code} for {token_symbol}")
            return None
            
    except requests.RequestException as e:
        logging.warning(f"Error fetching market data for {token_symbol}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error fetching market data: {e}")
        return None

def calculate_risk_metrics(event: Dict[str, Any]) -> Dict[str, float]:
    """
    Calculate additional risk metrics for the token
    """
    try:
        unlock_amount = float(event.get("unlock_amount", 0))
        circulating_supply = float(event.get("circulating_supply", 1))
        total_supply = float(event.get("total_supply", 1))
        
        # Calculate percentage of supply being unlocked
        unlock_percentage = (unlock_amount / circulating_supply) * 100 if circulating_supply > 0 else 0
        
        # Calculate how much of total supply is already circulating
        circulation_ratio = (circulating_supply / total_supply) * 100 if total_supply > 0 else 0
        
        # Calculate dilution impact
        new_circulating_supply = circulating_supply + unlock_amount
        dilution_impact = ((new_circulating_supply - circulating_supply) / circulating_supply) * 100 if circulating_supply > 0 else 0
        
        return {
            "unlock_percentage": unlock_percentage,
            "circulation_ratio": circulation_ratio,
            "dilution_impact": dilution_impact,
            "unlock_amount": unlock_amount,
            "circulating_supply": circulating_supply
        }
        
    except Exception as e:
        logging.error(f"Error calculating risk metrics: {e}")
        return {}

def is_high_impact_unlock(event: Dict[str, Any], threshold_percentage: float = 5.0) -> bool:
    """
    Determines if an unlock event represents a high-impact dilution event
    """
    try:
        risk_metrics = calculate_risk_metrics(event)
        unlock_percentage = risk_metrics.get("unlock_percentage", 0)
        
        return unlock_percentage >= threshold_percentage
        
    except Exception as e:
        logging.error(f"Error checking high impact unlock: {e}")
        return False
