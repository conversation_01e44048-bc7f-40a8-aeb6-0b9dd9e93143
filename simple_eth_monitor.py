#!/usr/bin/env python3
"""
Simple Real-time ETH Price Monitor
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def monitor_eth():
    print("📊 Real-time Ethereum Price Monitor")
    print("🔗 Using Binance WebSocket + REST API")
    print("💡 Press Ctrl+C to stop")
    print("=" * 40)
    
    try:
        from binance_websocket import BinanceWebSocketPriceFeed, get_realtime_price_binance
        
        # Initialize feed
        feed = BinanceWebSocketPriceFeed(use_testnet=False)
        
        # Subscribe to ETH
        print("📡 Connecting to Binance...")
        feed.subscribe_to_price('ETH')
        
        print("✅ Connected! Monitoring ETH price...\n")
        
        previous_price = None
        count = 0
        
        while True:
            try:
                # Get current price
                price = get_realtime_price_binance('ETH')
                
                if price:
                    count += 1
                    timestamp = datetime.now().strftime("%H:%M:%S")
                    
                    # Calculate change
                    change_str = ""
                    if previous_price:
                        change = price - previous_price
                        change_pct = (change / previous_price) * 100
                        
                        if change > 0:
                            change_str = f"📈 +${change:.2f} (+{change_pct:.2f}%)"
                        elif change < 0:
                            change_str = f"📉 ${change:.2f} ({change_pct:.2f}%)"
                        else:
                            change_str = "➡️ No change"
                    
                    print(f"[{timestamp}] 💰 ETH: ${price:.2f} {change_str}")
                    
                    previous_price = price
                else:
                    print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ No price data")
                
                time.sleep(3)  # Update every 3 seconds
                
            except KeyboardInterrupt:
                print("\n⏹️ Stopping monitor...")
                break
            except Exception as e:
                print(f"❌ Error: {e}")
                time.sleep(5)
        
        # Clean up
        feed.stop()
        print(f"📊 Total updates: {count}")
        print("✅ Monitor stopped")
        
    except Exception as e:
        print(f"❌ Failed to start monitor: {e}")

if __name__ == "__main__":
    monitor_eth()
