import os
import redis
import json
import logging
from decimal import Decimal
from telegram_bot import send_telegram_message

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Redis setup
REDIS_URL = os.environ.get("REDIS_URL")

def get_redis_connection():
    """Get Redis connection"""
    if not REDIS_URL:
        raise Exception("REDIS_URL environment variable not set")
    return redis.from_url(REDIS_URL)

# Subscribe to all relevant channels using a pattern
CHANNELS_TO_MONITOR = "chimera:*"

def format_message(channel: str, data: dict) -> str:
    """Formats event data into a human-readable string."""
    try:
        if "trade_candidates" in channel:
            return format_trade_candidate_message(data)
        elif "position_opened" in channel:
            return format_position_opened_message(data)
        elif "close_position" in channel:
            return format_close_position_message(data)
        elif "unlock_events" in channel:
            return format_unlock_event_message(data)
        elif "execution_errors" in channel:
            return format_execution_error_message(data)
        else:
            return format_generic_message(channel, data)
    except Exception as e:
        logging.error(f"Error formatting message for channel {channel}: {e}")
        return f"🚨 **Message Format Error** 🚨\nChannel: {channel}\nError: {str(e)}"

def format_trade_candidate_message(data: dict) -> str:
    """Format trade candidate notification"""
    token_symbol = data.get('token_symbol', 'Unknown')
    pressure_score = data.get('pressure_score', 0)
    unlock_date = data.get('unlock_date', 'Unknown')
    
    return f"""✅ **New Trade Candidate** ✅
🪙 Token: *{token_symbol}*
📊 Pressure Score: {pressure_score:.3f}
📅 Unlock Date: {unlock_date}
🎯 Strategy: Pre-unlock decay arbitrage"""

def format_position_opened_message(data: dict) -> str:
    """Format position opened notification"""
    token_symbol = data.get('token_symbol', 'Unknown')
    amount_shorted = data.get('amount_shorted', '0')
    entry_price = data.get('entry_price_in_usdc', '0')
    position_id = data.get('position_id', 'Unknown')
    
    try:
        entry_price_decimal = Decimal(str(entry_price))
        amount_decimal = Decimal(str(amount_shorted))
        total_value = entry_price_decimal * amount_decimal
    except:
        total_value = 0
    
    return f"""🚀 **Position Opened** 🚀
🪙 Token: *{token_symbol}*
📊 Position ID: {position_id}
💰 Amount Shorted: {amount_shorted}
💵 Entry Price: ${entry_price_decimal:.4f}
💎 Total Value: ${total_value:.2f}
📈 Status: OPEN"""

def format_close_position_message(data: dict) -> str:
    """Format close position notification"""
    position_id = data.get('position_id', 'Unknown')
    token_symbol = data.get('token_symbol', 'Unknown')
    reason = data.get('reason', 'Unknown reason')
    current_price = data.get('current_price', '0')
    entry_price = data.get('entry_price', '0')
    
    try:
        current_price_decimal = Decimal(str(current_price))
        entry_price_decimal = Decimal(str(entry_price))
        
        # Calculate P&L for short position
        price_change_pct = ((current_price_decimal - entry_price_decimal) / entry_price_decimal) * 100
        pnl_pct = -price_change_pct  # Negative for shorts
        
        pnl_emoji = "📈" if pnl_pct > 0 else "📉"
        pnl_status = "PROFIT" if pnl_pct > 0 else "LOSS"
        
    except:
        pnl_pct = 0
        pnl_emoji = "❓"
        pnl_status = "UNKNOWN"
    
    return f"""🛑 **Closing Position** 🛑
🪙 Token: *{token_symbol}*
📊 Position ID: {position_id}
⚠️ Reason: *{reason}*
💵 Entry: ${entry_price}
💵 Current: ${current_price}
{pnl_emoji} P&L: {pnl_pct:.2f}% {pnl_status}"""

def format_unlock_event_message(data: dict) -> str:
    """Format unlock event notification"""
    token_symbol = data.get('token_symbol', 'Unknown')
    unlock_date = data.get('unlock_date', 'Unknown')
    unlock_amount = data.get('unlock_amount', 0)
    
    try:
        unlock_amount_formatted = f"{float(unlock_amount):,.0f}"
    except:
        unlock_amount_formatted = str(unlock_amount)
    
    return f"""🔓 **Token Unlock Detected** 🔓
🪙 Token: *{token_symbol}*
📅 Unlock Date: {unlock_date}
💰 Amount: {unlock_amount_formatted} tokens
🔍 Analyzing for trade opportunities..."""

def format_execution_error_message(data: dict) -> str:
    """Format execution error notification"""
    token_symbol = data.get('token_symbol', 'Unknown')
    error = data.get('error', 'Unknown error')
    
    return f"""❌ **Execution Error** ❌
🪙 Token: *{token_symbol}*
🚨 Error: {error}
🔧 Manual intervention may be required"""

def format_generic_message(channel: str, data: dict) -> str:
    """Format generic message for unknown channels"""
    return f"""ℹ️ **System Event** ℹ️
📡 Channel: {channel}
📋 Data: `{json.dumps(data, indent=2)}`"""

def listen_and_notify():
    """Listens for all system events and sends Telegram notifications."""
    logging.info(f"The Herald is listening on channels: {CHANNELS_TO_MONITOR}")
    
    try:
        r = get_redis_connection()
        p = r.pubsub(ignore_subscribe_messages=True)
        p.psubscribe(CHANNELS_TO_MONITOR)
        
        for message in p.listen():
            try:
                if message['type'] == 'pmessage':
                    channel = message['channel'].decode('utf-8')
                    data = json.loads(message['data'])
                    
                    logging.info(f"Received message on channel: {channel}")
                    
                    formatted_text = format_message(channel, data)
                    send_telegram_message(formatted_text)
                    
            except json.JSONDecodeError as e:
                logging.error(f"Failed to decode JSON message: {message}. Error: {e}")
                error_text = f"🚨 **JSON Decode Error** 🚨\nChannel: {message.get('channel', 'Unknown')}\nError: {str(e)}"
                send_telegram_message(error_text)
                
            except Exception as e:
                logging.error(f"Herald failed to process message: {message}. Error: {e}")
                error_text = f"🚨 **Herald Error** 🚨\nCould not process message. Check logs.\n`{str(e)}`"
                send_telegram_message(error_text)
                
    except Exception as e:
        logging.error(f"Error in main listening loop: {e}")
        error_text = f"🚨 **Herald Critical Error** 🚨\nMain loop crashed: {str(e)}"
        try:
            send_telegram_message(error_text)
        except:
            pass  # If we can't send the error message, just log it
        raise

def send_startup_notification():
    """Send a notification when The Herald starts up"""
    try:
        startup_message = """🤖 **The Herald Online** 🤖
📡 Notification service started
🔔 Monitoring all Chimera system events
✅ Ready to send alerts"""
        send_telegram_message(startup_message)
    except Exception as e:
        logging.error(f"Failed to send startup notification: {e}")

def send_test_notification():
    """Send a test notification (useful for debugging)"""
    try:
        test_message = """🧪 **Test Notification** 🧪
📡 The Herald is working correctly
🔔 All systems operational
⏰ Timestamp: {datetime.now().isoformat()}"""
        send_telegram_message(test_message)
        logging.info("Test notification sent successfully")
    except Exception as e:
        logging.error(f"Failed to send test notification: {e}")

if __name__ == "__main__":
    # Send startup notification
    send_startup_notification()
    
    # Check if we should send a test notification
    if os.environ.get("SEND_TEST_NOTIFICATION", "false").lower() == "true":
        send_test_notification()
    
    # Start listening for events
    listen_and_notify()
