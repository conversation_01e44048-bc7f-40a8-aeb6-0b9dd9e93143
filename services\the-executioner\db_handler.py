import os
import psycopg2
import psycopg2.extras
from typing import Dict, Any, List
from datetime import datetime, timedelta
import logging

# Database connection details from <PERSON>der's environment
DB_URL = os.environ.get("DATABASE_URL")

def _get_db_conn():
    """Get database connection"""
    if not DB_URL:
        raise Exception("DATABASE_URL environment variable not set")
    return psycopg2.connect(DB_URL)

def log_trade_entry(token_symbol: str, token_address: str, amount_shorted: float, 
                   entry_price: float, unlock_date: str, borrow_tx_hash: str = None, 
                   swap_tx_hash: str = None) -> int:
    """Log a new trade position to the database"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO positions 
                (token_symbol, token_address, amount_shorted, entry_price_in_usdc, 
                 unlock_date, borrow_tx_hash, swap_tx_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (token_symbol, token_address, amount_shorted, entry_price, 
                  unlock_date, borrow_tx_hash, swap_tx_hash))
            
            position_id = cur.fetchone()[0]
            conn.commit()
            logging.info(f"Logged new position with ID: {position_id}")
            return position_id
    except Exception as e:
        conn.rollback()
        logging.error(f"Error logging trade entry: {e}")
        raise
    finally:
        conn.close()

def update_position_status(position_id: int, status: str, close_tx_hash: str = None):
    """Update position status"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            if status == 'CLOSED':
                cur.execute("""
                    UPDATE positions 
                    SET status = %s, close_tx_hash = %s, closed_at = NOW()
                    WHERE id = %s
                """, (status, close_tx_hash, position_id))
            else:
                cur.execute("""
                    UPDATE positions 
                    SET status = %s
                    WHERE id = %s
                """, (status, position_id))
            
            conn.commit()
            logging.info(f"Updated position {position_id} status to {status}")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error updating position status: {e}")
        raise
    finally:
        conn.close()

def get_open_positions() -> List[Dict[str, Any]]:
    """Retrieves all open trading positions"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, created_at
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """)
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying open positions: {e}")
        return []
    finally:
        conn.close()

def get_position_by_id(position_id: int) -> Dict[str, Any]:
    """Get a specific position by ID"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, close_tx_hash,
                       created_at, closed_at
                FROM positions 
                WHERE id = %s
            """, (position_id,))
            
            result = cur.fetchone()
            return dict(result) if result else {}
    except Exception as e:
        logging.error(f"Error querying position {position_id}: {e}")
        return {}
    finally:
        conn.close()

def get_positions_by_token(token_address: str) -> List[Dict[str, Any]]:
    """Get all positions for a specific token"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status,
                       borrow_tx_hash, swap_tx_hash, close_tx_hash,
                       created_at, closed_at
                FROM positions 
                WHERE token_address = %s
                ORDER BY created_at DESC
            """, (token_address,))
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying positions for token {token_address}: {e}")
        return []
    finally:
        conn.close()
