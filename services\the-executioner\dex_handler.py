import logging
import requests
from decimal import Decimal
from web3 import Web3
from eth_account import Account

# 1inch API endpoint for Ethereum mainnet
ONEINCH_API_BASE = "https://api.1inch.io/v5.0/1"

def swap_tokens(web3: Web3, account: Account, from_token: str, to_token: str, amount: Decimal) -> tuple:
    """
    Swaps tokens using 1inch DEX aggregator
    
    Args:
        web3: Web3 instance
        account: Account to swap with
        from_token: Address of token to swap from
        to_token: Address of token to swap to
        amount: Amount to swap (in token units, not wei)
    
    Returns:
        Tuple of (transaction_hash, amount_out) if successful, (None, None) if failed
    """
    try:
        # Get token decimals
        from_decimals = get_token_decimals(web3, from_token)
        to_decimals = get_token_decimals(web3, to_token)
        
        # Convert amount to wei
        amount_wei = int(amount * (10 ** from_decimals))
        
        logging.info(f"Swapping {amount} tokens ({amount_wei} wei) from {from_token} to {to_token}")
        
        # Get quote from 1inch
        quote_url = f"{ONEINCH_API_BASE}/quote"
        quote_params = {
            "fromTokenAddress": from_token,
            "toTokenAddress": to_token,
            "amount": str(amount_wei)
        }
        
        quote_response = requests.get(quote_url, params=quote_params, timeout=10)
        quote_response.raise_for_status()
        quote_data = quote_response.json()
        
        expected_amount_out = int(quote_data["toTokenAmount"])
        expected_amount_out_formatted = expected_amount_out / (10 ** to_decimals)
        
        logging.info(f"1inch quote: {expected_amount_out_formatted} {to_token}")
        
        # Get swap transaction data
        swap_url = f"{ONEINCH_API_BASE}/swap"
        swap_params = {
            "fromTokenAddress": from_token,
            "toTokenAddress": to_token,
            "amount": str(amount_wei),
            "fromAddress": account.address,
            "slippage": "1",  # 1% slippage tolerance
            "disableEstimate": "true"
        }
        
        swap_response = requests.get(swap_url, params=swap_params, timeout=10)
        swap_response.raise_for_status()
        swap_data = swap_response.json()
        
        # Extract transaction data
        tx_data = swap_data["tx"]
        
        # First approve the 1inch router to spend our tokens
        router_address = tx_data["to"]
        approve_tx_hash = approve_token_spending(web3, account, from_token, router_address, amount_wei)
        if not approve_tx_hash:
            raise Exception("Failed to approve token spending for 1inch router")
        
        # Build and send the swap transaction
        transaction = {
            'to': tx_data["to"],
            'data': tx_data["data"],
            'value': int(tx_data["value"]),
            'gas': int(tx_data["gas"]),
            'gasPrice': int(tx_data["gasPrice"]),
            'nonce': web3.eth.get_transaction_count(account.address)
        }
        
        # Sign and send transaction
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            # Calculate actual amount received (this is simplified)
            actual_amount_out = expected_amount_out_formatted
            logging.info(f"Swap successful. Tx hash: {tx_hash.hex()}, Amount out: {actual_amount_out}")
            return tx_hash.hex(), actual_amount_out
        else:
            logging.error(f"Swap transaction failed. Tx hash: {tx_hash.hex()}")
            return None, None
            
    except Exception as e:
        logging.error(f"Error swapping tokens: {e}")
        return None, None

def swap_tokens_uniswap(web3: Web3, account: Account, from_token: str, to_token: str, amount: Decimal) -> tuple:
    """
    Alternative swap function using Uniswap V3 directly
    This is a simplified implementation - in production you'd want more sophisticated routing
    """
    try:
        # Uniswap V3 SwapRouter address
        UNISWAP_V3_ROUTER = "0xE592427A0AEce92De3Edee1F18E0157C05861564"
        
        # Simplified Uniswap V3 Router ABI
        router_abi = [
            {
                "inputs": [
                    {
                        "components": [
                            {"internalType": "address", "name": "tokenIn", "type": "address"},
                            {"internalType": "address", "name": "tokenOut", "type": "address"},
                            {"internalType": "uint24", "name": "fee", "type": "uint24"},
                            {"internalType": "address", "name": "recipient", "type": "address"},
                            {"internalType": "uint256", "name": "deadline", "type": "uint256"},
                            {"internalType": "uint256", "name": "amountIn", "type": "uint256"},
                            {"internalType": "uint256", "name": "amountOutMinimum", "type": "uint256"},
                            {"internalType": "uint160", "name": "sqrtPriceLimitX96", "type": "uint160"}
                        ],
                        "internalType": "struct ISwapRouter.ExactInputSingleParams",
                        "name": "params",
                        "type": "tuple"
                    }
                ],
                "name": "exactInputSingle",
                "outputs": [{"internalType": "uint256", "name": "amountOut", "type": "uint256"}],
                "stateMutability": "payable",
                "type": "function"
            }
        ]
        
        # Get token decimals
        from_decimals = get_token_decimals(web3, from_token)
        amount_wei = int(amount * (10 ** from_decimals))
        
        # Create router contract instance
        router_contract = web3.eth.contract(address=UNISWAP_V3_ROUTER, abi=router_abi)
        
        # Approve router to spend tokens
        approve_tx_hash = approve_token_spending(web3, account, from_token, UNISWAP_V3_ROUTER, amount_wei)
        if not approve_tx_hash:
            raise Exception("Failed to approve token spending for Uniswap router")
        
        # Prepare swap parameters
        deadline = web3.eth.get_block('latest')['timestamp'] + 300  # 5 minutes from now
        
        swap_params = {
            "tokenIn": from_token,
            "tokenOut": to_token,
            "fee": 3000,  # 0.3% fee tier
            "recipient": account.address,
            "deadline": deadline,
            "amountIn": amount_wei,
            "amountOutMinimum": 0,  # Accept any amount of tokens out
            "sqrtPriceLimitX96": 0
        }
        
        # Build swap transaction
        swap_tx = router_contract.functions.exactInputSingle(swap_params)
        
        gas_estimate = swap_tx.estimate_gas({'from': account.address})
        gas_price = web3.eth.gas_price
        
        transaction = swap_tx.build_transaction({
            'from': account.address,
            'gas': int(gas_estimate * 1.2),
            'gasPrice': gas_price,
            'nonce': web3.eth.get_transaction_count(account.address)
        })
        
        # Sign and send
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            # Extract amount out from logs (simplified)
            amount_out = 0  # In production, parse the logs to get exact amount
            logging.info(f"Uniswap swap successful. Tx hash: {tx_hash.hex()}")
            return tx_hash.hex(), amount_out
        else:
            logging.error(f"Uniswap swap failed. Tx hash: {tx_hash.hex()}")
            return None, None
            
    except Exception as e:
        logging.error(f"Error with Uniswap swap: {e}")
        return None, None

def get_token_decimals(web3: Web3, token_address: str) -> int:
    """Get the decimals of an ERC20 token"""
    try:
        erc20_abi = [
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        
        contract = web3.eth.contract(address=token_address, abi=erc20_abi)
        decimals = contract.functions.decimals().call()
        return decimals
        
    except Exception as e:
        logging.error(f"Error getting decimals for {token_address}: {e}")
        return 18  # Default to 18 decimals

def approve_token_spending(web3: Web3, account: Account, token_address: str, spender: str, amount: int) -> str:
    """Approve a spender to spend tokens on behalf of the account"""
    try:
        erc20_abi = [
            {
                "constant": False,
                "inputs": [
                    {"name": "_spender", "type": "address"},
                    {"name": "_value", "type": "uint256"}
                ],
                "name": "approve",
                "outputs": [{"name": "", "type": "bool"}],
                "type": "function"
            }
        ]
        
        contract = web3.eth.contract(address=token_address, abi=erc20_abi)
        
        # Build approve transaction
        approve_tx = contract.functions.approve(spender, amount)
        
        gas_estimate = approve_tx.estimate_gas({'from': account.address})
        gas_price = web3.eth.gas_price
        
        transaction = approve_tx.build_transaction({
            'from': account.address,
            'gas': int(gas_estimate * 1.2),
            'gasPrice': gas_price,
            'nonce': web3.eth.get_transaction_count(account.address)
        })
        
        # Sign and send
        signed_txn = account.sign_transaction(transaction)
        tx_hash = web3.eth.send_raw_transaction(signed_txn.rawTransaction)
        
        # Wait for confirmation
        receipt = web3.eth.wait_for_transaction_receipt(tx_hash, timeout=300)
        
        if receipt.status == 1:
            logging.info(f"Token approval successful. Tx hash: {tx_hash.hex()}")
            return tx_hash.hex()
        else:
            logging.error(f"Token approval failed. Tx hash: {tx_hash.hex()}")
            return None
            
    except Exception as e:
        logging.error(f"Error approving token spending: {e}")
        return None
