"""
Modern Oracle Service Tests - 2025 Best Practices
================================================

Enhanced test suite for The Oracle service using modern pytest patterns:
- Fixture-based test data
- Parameterized tests
- Performance testing
- Mock API responses
- Async testing support
- Comprehensive error handling tests
"""

import pytest
import sys
import os
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta
import requests

# Add the oracle service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'services', 'the-oracle'))

from data_sources import (
    fetch_token_unlocks_data, 
    normalize_unlock_event,
    fetch_from_coingecko_api,
    fetch_defillama_tvl_analysis,
    generate_placeholder_address,
    estimate_unlock_date_from_tvl_drop
)
from event_publisher import publish_unlock_event


class TestDataNormalization:
    """Test the data normalization layer"""
    
    def test_normalize_unlock_event_complete_data(self, sample_unlock_event):
        """Test normalization with complete data"""
        result = normalize_unlock_event(sample_unlock_event, "test_source")
        
        assert result is not None
        assert result['token_symbol'] == sample_unlock_event['token_symbol']
        assert result['contract_address'] == sample_unlock_event['contract_address']
        assert result['unlock_date'] == sample_unlock_event['unlock_date']
        assert result['source'] == "test_source"
        assert 'created_at' in result
    
    def test_normalize_unlock_event_missing_fields(self):
        """Test normalization with missing fields"""
        incomplete_event = {
            'symbol': 'TEST',  # Different field name
            'amount': 1000000
        }
        
        result = normalize_unlock_event(incomplete_event, "test_source")
        
        assert result is not None
        assert result['token_symbol'] == 'TEST'
        assert result['contract_address'].startswith('0x')
        assert len(result['contract_address']) == 42
        assert result['unlock_date'] is not None
    
    def test_normalize_unlock_event_tvl_source(self):
        """Test normalization for TVL analysis source"""
        tvl_event = {
            'name': 'Test Protocol',
            'tvl_current': 50000000,
            'tvl_change_1d': -20.5,
            'tvl_change_7d': -30.2
        }
        
        result = normalize_unlock_event(tvl_event, "DeFiLlama_TVL_Analysis")
        
        assert result is not None
        assert result['token_symbol'] == 'Test Protocol'
        assert 'tvl_current' in result
        assert 'unlock_indicator' in result
    
    @pytest.mark.parametrize("invalid_data", [
        {},  # Empty dict
        {'name': ''},  # Empty name
        {'symbol': None}  # None symbol
    ])
    def test_normalize_unlock_event_invalid_data(self, invalid_data):
        """Test normalization with invalid data"""
        result = normalize_unlock_event(invalid_data, "test_source")
        assert result is None


class TestAddressGeneration:
    """Test address generation utilities"""
    
    def test_generate_placeholder_address_deterministic(self):
        """Test that address generation is deterministic"""
        symbol = "TEST"
        addr1 = generate_placeholder_address(symbol)
        addr2 = generate_placeholder_address(symbol)
        
        assert addr1 == addr2
        assert addr1.startswith('0x')
        assert len(addr1) == 42
    
    def test_generate_placeholder_address_different_symbols(self):
        """Test that different symbols generate different addresses"""
        addr1 = generate_placeholder_address("TEST1")
        addr2 = generate_placeholder_address("TEST2")
        
        assert addr1 != addr2
        assert both_valid_addresses(addr1, addr2)
    
    def test_generate_placeholder_address_length(self):
        """Test address length is always correct"""
        symbols = ["A", "VERY_LONG_TOKEN_SYMBOL_NAME", "123", ""]
        
        for symbol in symbols:
            addr = generate_placeholder_address(symbol)
            assert len(addr) == 42
            assert addr.startswith('0x')


class TestDateEstimation:
    """Test unlock date estimation functions"""
    
    @pytest.mark.parametrize("tvl_change_1d,tvl_change_7d,expected_days", [
        (-25, -10, 3),   # Severe 1-day drop
        (-10, -35, 7),   # Severe 7-day drop
        (-5, -10, 14),   # Moderate changes
        (0, 0, 14)       # No changes
    ])
    def test_estimate_unlock_date_from_tvl_drop(self, tvl_change_1d, tvl_change_7d, expected_days):
        """Test TVL-based date estimation"""
        event = {
            'tvl_change_1d': tvl_change_1d,
            'tvl_change_7d': tvl_change_7d
        }
        
        result = estimate_unlock_date_from_tvl_drop(event)
        estimated_date = datetime.fromisoformat(result.replace('Z', '+00:00'))
        days_ahead = (estimated_date - datetime.now(timezone.utc)).days
        
        assert abs(days_ahead - expected_days) <= 1  # Allow 1 day tolerance


class TestCoinGeckoIntegration:
    """Test CoinGecko API integration"""
    
    @patch('data_sources.requests.get')
    def test_fetch_from_coingecko_api_success(self, mock_get, mock_api_responses):
        """Test successful CoinGecko API call"""
        # Setup mock response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = mock_api_responses['coingecko_markets']
        mock_get.return_value = mock_response
        
        # Set API key
        with patch.dict(os.environ, {'COINGECKO_API_KEY': 'test_key'}):
            result = fetch_from_coingecko_api()
        
        assert isinstance(result, list)
        mock_get.assert_called()
    
    @patch('data_sources.requests.get')
    def test_fetch_from_coingecko_api_rate_limit(self, mock_get):
        """Test CoinGecko API rate limiting"""
        # Setup rate limit response
        mock_response = MagicMock()
        mock_response.status_code = 429
        mock_response.raise_for_status.side_effect = requests.HTTPError("Rate limited")
        mock_get.return_value = mock_response
        
        with patch.dict(os.environ, {'COINGECKO_API_KEY': 'test_key'}):
            result = fetch_from_coingecko_api()
        
        # Should handle rate limiting gracefully
        assert isinstance(result, list)
    
    def test_fetch_from_coingecko_api_no_key(self):
        """Test CoinGecko API without API key"""
        with patch.dict(os.environ, {}, clear=True):
            result = fetch_from_coingecko_api()
        
        assert result == []


class TestDeFiLlamaIntegration:
    """Test DeFiLlama API integration"""
    
    @patch('data_sources.requests.get')
    def test_fetch_defillama_tvl_analysis_success(self, mock_get, mock_api_responses):
        """Test successful DeFiLlama TVL analysis"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = mock_api_responses['defillama_protocols']
        mock_get.return_value = mock_response
        
        result = fetch_defillama_tvl_analysis()
        
        assert isinstance(result, list)
        mock_get.assert_called_with("https://api.llama.fi/protocols", timeout=10)
    
    @patch('data_sources.requests.get')
    def test_fetch_defillama_tvl_analysis_network_error(self, mock_get):
        """Test DeFiLlama API network error handling"""
        mock_get.side_effect = requests.ConnectionError("Network error")
        
        result = fetch_defillama_tvl_analysis()
        
        assert result == []


class TestEventPublishing:
    """Test event publishing functionality"""
    
    def test_publish_unlock_event_success(self, mock_redis, sample_unlock_event):
        """Test successful event publishing"""
        with patch('event_publisher.get_redis_connection', return_value=mock_redis):
            publish_unlock_event(sample_unlock_event)
        
        mock_redis.publish.assert_called_once()
        call_args = mock_redis.publish.call_args
        
        assert call_args[0][0] == "chimera:unlock_events"
        assert sample_unlock_event['token_symbol'] in call_args[0][1]
    
    def test_publish_unlock_event_redis_error(self, sample_unlock_event):
        """Test event publishing with Redis error"""
        mock_redis = MagicMock()
        mock_redis.publish.side_effect = Exception("Redis connection failed")
        
        with patch('event_publisher.get_redis_connection', return_value=mock_redis):
            # Should not raise exception
            publish_unlock_event(sample_unlock_event)


class TestMainDataFetching:
    """Test main data fetching functionality"""
    
    @patch('data_sources.fetch_from_defillama_api')
    @patch('data_sources.fetch_from_coingecko_api')
    @patch('data_sources.get_curated_unlock_events')
    def test_fetch_token_unlocks_data_integration(self, mock_curated, mock_coingecko, mock_defillama, test_data_factory):
        """Test integrated data fetching from multiple sources"""
        # Setup mock returns
        mock_curated.return_value = test_data_factory.create_unlock_events(2)
        mock_coingecko.return_value = []
        mock_defillama.return_value = []
        
        result = fetch_token_unlocks_data()
        
        assert isinstance(result, list)
        assert len(result) >= 2  # At least the curated events
        
        # Verify all sources were called
        mock_curated.assert_called_once()
        mock_coingecko.assert_called_once()
        mock_defillama.assert_called_once()
    
    def test_fetch_token_unlocks_data_error_isolation(self, test_data_factory):
        """Test that errors in one source don't affect others"""
        with patch('data_sources.fetch_from_defillama_api', side_effect=Exception("DeFiLlama error")), \
             patch('data_sources.fetch_from_coingecko_api', return_value=[]), \
             patch('data_sources.get_curated_unlock_events', return_value=test_data_factory.create_unlock_events(1)):
            
            result = fetch_token_unlocks_data()
            
            # Should still return curated events despite DeFiLlama error
            assert isinstance(result, list)
            assert len(result) >= 1


class TestPerformance:
    """Performance tests for Oracle service"""
    
    @pytest.mark.slow
    def test_fetch_token_unlocks_data_performance(self, performance_timer):
        """Test that data fetching completes within reasonable time"""
        performance_timer.start()
        
        with patch('data_sources.get_curated_unlock_events', return_value=[]):
            fetch_token_unlocks_data()
        
        elapsed = performance_timer.stop()
        
        # Should complete within 30 seconds (generous for API calls)
        assert elapsed < 30.0
    
    def test_normalize_unlock_event_performance(self, performance_timer, sample_unlock_event):
        """Test normalization performance"""
        performance_timer.start()
        
        # Normalize 1000 events
        for _ in range(1000):
            normalize_unlock_event(sample_unlock_event, "test_source")
        
        elapsed = performance_timer.stop()
        
        # Should process 1000 events in under 1 second
        assert elapsed < 1.0


# Helper functions
def both_valid_addresses(addr1: str, addr2: str) -> bool:
    """Check if both addresses are valid"""
    return (addr1.startswith('0x') and len(addr1) == 42 and
            addr2.startswith('0x') and len(addr2) == 42)


# Integration test with external APIs (marked as slow)
@pytest.mark.slow
@pytest.mark.api
class TestExternalAPIIntegration:
    """Integration tests with real external APIs (requires network)"""
    
    def test_real_defillama_api_call(self):
        """Test actual DeFiLlama API call (requires network)"""
        try:
            result = fetch_defillama_tvl_analysis()
            assert isinstance(result, list)
        except Exception as e:
            pytest.skip(f"External API not available: {e}")
    
    @pytest.mark.skipif(not os.getenv('COINGECKO_API_KEY'), reason="CoinGecko API key not set")
    def test_real_coingecko_api_call(self):
        """Test actual CoinGecko API call (requires API key)"""
        try:
            result = fetch_from_coingecko_api()
            assert isinstance(result, list)
        except Exception as e:
            pytest.skip(f"External API not available: {e}")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
