import os
import redis
import json
import logging
from typing import Dict, Any

from analysis import calculate_unlock_pressure_score
from onchain_checker import is_token_borrowable

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Environment variables
PRESSURE_SCORE_THRESHOLD = float(os.environ.get("PRESSURE_SCORE_THRESHOLD", "0.75"))

# Redis setup
REDIS_URL = os.environ.get("REDIS_URL")

def get_redis_connection():
    """Get Redis connection"""
    if not REDIS_URL:
        raise Exception("REDIS_URL environment variable not set")
    return redis.from_url(REDIS_URL)

UNLOCK_EVENT_CHANNEL = "chimera:unlock_events"
TRADE_CANDIDATE_CHANNEL = "chimera:trade_candidates"

def process_unlock_event(event: Dict[str, Any]):
    """Analyzes an unlock event and publishes a trade candidate if it meets criteria."""
    logging.info(f"The Seer received event: {event.get('token_symbol')}")
    
    # 1. Calculate the Unlock Pressure Score
    score = calculate_unlock_pressure_score(event)
    logging.info(f"Calculated Pressure Score for {event.get('token_symbol')}: {score:.2f}")

    if score < PRESSURE_SCORE_THRESHOLD:
        logging.info("Score is below threshold. Discarding event.")
        return

    # 2. Check if the token is borrowable on a lending protocol
    contract_address = event.get('contract_address')
    if not is_token_borrowable(contract_address):
        logging.info(f"Token {event.get('token_symbol')} is not borrowable on Aave/Compound. Discarding.")
        return
    
    # 3. All checks passed. Publish as a trade candidate.
    trade_candidate = {
        "token_symbol": event.get('token_symbol'),
        "contract_address": contract_address,
        "unlock_date": event.get('unlock_date'),
        "strategy_id": "pre_unlock_decay_v1",
        "pressure_score": score,
        "unlock_amount": event.get('unlock_amount'),
        "circulating_supply": event.get('circulating_supply')
    }
    
    try:
        r = get_redis_connection()
        message = json.dumps(trade_candidate, default=str)
        r.publish(TRADE_CANDIDATE_CHANNEL, message)
        logging.warning(f"HIGH CONVICTION: Published TRADE_CANDIDATE for {event.get('token_symbol')}")
    except Exception as e:
        logging.error(f"Error publishing trade candidate: {e}")

def listen_for_events():
    """Main loop to listen for unlock events from Redis."""
    logging.info("The Seer is now listening for unlock events...")
    
    try:
        r = get_redis_connection()
        p = r.pubsub(ignore_subscribe_messages=True)
        p.subscribe(UNLOCK_EVENT_CHANNEL)
        
        for message in p.listen():
            try:
                if message['type'] == 'message':
                    event_data = json.loads(message['data'])
                    process_unlock_event(event_data)
            except (json.JSONDecodeError, KeyError) as e:
                logging.error(f"Failed to process message: {message}. Error: {e}")
            except Exception as e:
                logging.error(f"Unexpected error processing message: {e}")
                
    except Exception as e:
        logging.error(f"Error in main listening loop: {e}")
        raise

if __name__ == "__main__":
    listen_for_events()
