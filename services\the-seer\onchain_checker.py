import requests
import logging
from typing import Optional, Dict, Any

# Aave V3 Subgraph URLs - Multiple endpoints to try
# Note: The Graph has migrated to decentralized network, endpoints may change
AAVE_V3_ENDPOINTS = [
    "https://gateway-arbitrum.network.thegraph.com/api/subgraphs/id/Cd2gEDVeqnjBn1hSeqFMitw8Q1iiyV9FYUZkLNRcL87g",
    "https://api.thegraph.com/subgraphs/id/Cd2gEDVeqnjBn1hSeqFMitw8Q1iiyV9FYUZkLNRcL87g",
    "https://api.thegraph.com/subgraphs/name/aave/protocol-v3"
]

# Compound V3 - Disabled due to deprecated endpoints
COMPOUND_V3_SUBGRAPH_URL = None

# Enable/disable external API checks (can be turned off for reliability)
ENABLE_EXTERNAL_API_CHECKS = False  # Set to True to enable API checks

def is_token_borrowable(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on major lending protocols.

    Strategy (prioritizes reliability over real-time accuracy):
    1. Check major token whitelist first (fast and reliable)
    2. Optionally try external API checks if enabled
    3. Default to False for unknown tokens

    This approach ensures the system works even when external APIs fail.
    """
    if not contract_address:
        return False

    # Primary check: Major token whitelist (always reliable)
    if is_major_token(contract_address):
        logging.info(f"✅ Token {contract_address} is a major token, confirmed borrowable")
        return True

    # Secondary check: External APIs (only if enabled and for non-major tokens)
    if ENABLE_EXTERNAL_API_CHECKS:
        logging.info(f"🔍 Checking external APIs for {contract_address}")

        try:
            if is_borrowable_on_aave(contract_address):
                logging.info(f"✅ Token {contract_address} confirmed borrowable on Aave V3")
                return True
        except Exception as e:
            logging.warning(f"⚠️ Aave API check failed for {contract_address}: {e}")

        try:
            if is_borrowable_on_compound(contract_address):
                logging.info(f"✅ Token {contract_address} confirmed borrowable on Compound V3")
                return True
        except Exception as e:
            logging.warning(f"⚠️ Compound API check failed for {contract_address}: {e}")
    else:
        logging.info(f"🔒 External API checks disabled, relying on whitelist only")

    logging.info(f"❌ Token {contract_address} not found in borrowable token list")
    return False

def is_borrowable_on_aave(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Aave V3 by querying its subgraph.
    Tries multiple endpoints and query formats for maximum reliability.
    """
    if not ENABLE_EXTERNAL_API_CHECKS:
        logging.info("External API checks disabled for Aave")
        return False

    # GraphQL queries to try (different formats for different subgraph versions)
    queries_to_try = [
        # Format 1: Search by underlyingAsset (most reliable)
        f"""
        {{
            reserves(where: {{underlyingAsset: "{contract_address.lower()}"}}) {{
                id
                borrowingEnabled
                isActive
                isFrozen
                symbol
                name
                underlyingAsset
            }}
        }}
        """,
        # Format 2: Direct reserve lookup
        f"""
        {{
            reserve(id: "{contract_address.lower()}") {{
                id
                borrowingEnabled
                isActive
                isFrozen
                symbol
                name
            }}
        }}
        """
    ]

    # Try each endpoint with each query format
    for endpoint_idx, endpoint in enumerate(AAVE_V3_ENDPOINTS):
        logging.info(f"Trying Aave endpoint {endpoint_idx + 1}/{len(AAVE_V3_ENDPOINTS)}")

        for query_idx, query in enumerate(queries_to_try):
            try:
                logging.debug(f"Query {query_idx + 1} on endpoint {endpoint_idx + 1}")

                response = requests.post(
                    endpoint,
                    json={'query': query},
                    timeout=10,
                    headers={'Content-Type': 'application/json'}
                )
                response.raise_for_status()
                data = response.json()

                if 'errors' in data:
                    logging.warning(f"GraphQL errors: {data['errors']}")
                    continue  # Try next query/endpoint

                # Handle single reserve response
                reserve_data = data.get("data", {}).get("reserve")
                if reserve_data and _check_reserve_borrowable(reserve_data):
                    logging.info(f"✅ Found borrowable reserve: {reserve_data.get('symbol', 'Unknown')}")
                    return True

                # Handle multiple reserves response
                reserves_data = data.get("data", {}).get("reserves", [])
                for reserve in reserves_data:
                    if _check_reserve_borrowable(reserve):
                        logging.info(f"✅ Found borrowable reserve: {reserve.get('symbol', 'Unknown')}")
                        return True

            except requests.RequestException as e:
                logging.warning(f"Request error on endpoint {endpoint_idx + 1}: {e}")
                continue
            except Exception as e:
                logging.error(f"Unexpected error: {e}")
                continue

    logging.info(f"❌ No borrowable reserves found for {contract_address} on any endpoint")
    return False

def _check_reserve_borrowable(reserve_data: dict) -> bool:
    """Check if a reserve is borrowable based on its data"""
    return (
        reserve_data.get("borrowingEnabled") is True and
        reserve_data.get("isActive") is True and
        reserve_data.get("isFrozen") is False
    )

# Backup endpoint function removed - now handled in main function with multiple endpoints

def is_borrowable_on_compound(contract_address: str) -> bool:
    """
    Checks if a token is available for borrowing on Compound V3.

    NOTE: Compound subgraphs have been deprecated on The Graph.
    This function now returns False and logs a warning.
    In production, you would implement on-chain contract calls here.
    """
    if COMPOUND_V3_SUBGRAPH_URL is None:
        logging.info("Compound V3 subgraph checking is disabled (endpoint deprecated)")
        return False

    logging.warning("Compound V3 subgraph has been deprecated. Skipping Compound check.")
    logging.info("To enable Compound checking, implement direct contract calls to Compound V3 Comet contracts")
    return False

def is_major_token(contract_address: str) -> bool:
    """
    Fallback check for major tokens that are likely to be borrowable on Aave V3.
    This is an expanded whitelist of well-known token addresses that are typically available.
    Updated based on current Aave V3 markets.
    """
    major_tokens = {
        # Core DeFi tokens
        "******************************************": "UNI",
        "******************************************": "AAVE",
        "******************************************": "DAI",
        "******************************************": "USDC",
        "******************************************": "USDT",
        "******************************************": "WBTC",
        "******************************************": "WETH",
        "******************************************": "LINK",
        "******************************************": "MKR",

        # Additional major tokens commonly on Aave
        "******************************************": "COMP",
        "0x92d6c1e31e14519d225d5829cf70af773944c7f": "DYDX",
        "******************************************": "CVX",
        "******************************************": "CRV",
        "******************************************": "DPI",
        "******************************************": "FRAX",
        "******************************************": "FEI",
        "******************************************": "YFI",
        "******************************************": "REP",
        "******************************************": "ZRX",

        # Stablecoins
        "******************************************": "USDP",
        "******************************************": "sUSD",
        "******************************************": "TUSD",

        # Liquid staking tokens
        "******************************************": "stETH",
        "******************************************": "wstETH",
        "******************************************": "cbETH",
        "******************************************": "sFRAX"
    }

    return contract_address.lower() in major_tokens

def get_lending_protocols_info(contract_address: str) -> Dict[str, Any]:
    """
    Get detailed information about token availability across lending protocols
    """
    info = {
        "aave_v3": {"available": False, "borrowing_enabled": False},
        "compound_v3": {"available": False, "borrowing_enabled": False},
        "is_major_token": is_major_token(contract_address)
    }
    
    # Check Aave
    try:
        if is_borrowable_on_aave(contract_address):
            info["aave_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Aave for {contract_address}: {e}")
    
    # Check Compound
    try:
        if is_borrowable_on_compound(contract_address):
            info["compound_v3"] = {"available": True, "borrowing_enabled": True}
    except Exception as e:
        logging.error(f"Error checking Compound for {contract_address}: {e}")
    
    return info
