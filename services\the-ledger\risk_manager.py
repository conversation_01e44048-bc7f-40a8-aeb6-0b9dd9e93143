import os
import logging
from decimal import Decimal
from datetime import datetime, timedelta, timezone
from typing import <PERSON><PERSON>, Dict, Any

# Risk management parameters from environment variables
STOP_LOSS_PCT = Decimal(os.environ.get("STOP_LOSS_PCT", "0.15"))  # 15%
TAKE_PROFIT_DAYS_BEFORE_UNLOCK = int(os.environ.get("TAKE_PROFIT_DAYS_BEFORE_UNLOCK", "1"))
TAKE_PROFIT_PCT = Decimal(os.environ.get("TAKE_PROFIT_PCT", "0.10"))  # 10% profit target

def check_risk_rules(position: Dict[str, Any], current_price: Decimal) -> Tuple[str, str]:
    """
    Checks an open short position against stop-loss and take-profit rules.
    Returns ('CLOSE', 'reason') or ('HOLD', None).

    CRITICAL: Risk rules are checked in priority order:
    1. STOP-LOSS (highest priority - capital preservation)
    2. TAKE-PROFIT (price-based)
    3. TIME-BASED EXIT (lowest priority)
    """
    try:
        entry_price = Decimal(str(position['entry_price_in_usdc']))
        position_id = position.get('position_id', 'unknown')
        token_symbol = position.get('token_symbol', 'unknown')

        logging.info(f"Checking risk rules for position {position_id} ({token_symbol})")
        logging.info(f"  Entry price: ${entry_price:.4f}")
        logging.info(f"  Current price: ${current_price:.4f}")

        # PRIORITY 1: Check Stop-Loss (for a short, a price increase is a loss)
        # This MUST be checked first to protect capital
        stop_loss_price = entry_price * (1 + STOP_LOSS_PCT)
        logging.info(f"  Stop-loss price: ${stop_loss_price:.4f}")

        if current_price >= stop_loss_price:
            loss_pct = ((current_price - entry_price) / entry_price) * 100
            reason = f"Stop-Loss triggered at ${current_price:.4f} (Limit: ${stop_loss_price:.4f}, Loss: {loss_pct:.2f}%)"
            logging.warning(f"🚨 STOP-LOSS TRIGGERED: {reason}")
            return "CLOSE", reason

        # PRIORITY 2: Check Take-Profit based on price (for shorts, profit when price goes down)
        take_profit_price = entry_price * (1 - TAKE_PROFIT_PCT)
        logging.info(f"  Take-profit price: ${take_profit_price:.4f}")

        if current_price <= take_profit_price:
            profit_pct = ((entry_price - current_price) / entry_price) * 100
            reason = f"Take-Profit triggered at ${current_price:.4f} (Target: ${take_profit_price:.4f}, Profit: {profit_pct:.2f}%)"
            logging.warning(f"🎯 TAKE-PROFIT TRIGGERED: {reason}")
            return "CLOSE", reason

        # PRIORITY 3: Check Time-based exit (exit before unlock)
        # Only check this if neither stop-loss nor take-profit were triggered
        unlock_date_str = position.get('unlock_date')
        if unlock_date_str:
            time_based_exit = check_time_based_exit(unlock_date_str)
            if time_based_exit:
                logging.warning(f"⏰ TIME-BASED EXIT TRIGGERED: {time_based_exit}")
                return "CLOSE", time_based_exit

        # Calculate current P&L for logging
        pnl_pct = ((entry_price - current_price) / entry_price) * 100
        pnl_status = "profit" if pnl_pct > 0 else "loss"

        # Log distance to risk levels for monitoring
        distance_to_stop_loss = ((stop_loss_price - current_price) / current_price) * 100
        distance_to_take_profit = ((current_price - take_profit_price) / current_price) * 100

        logging.info(f"  Distance to stop-loss: {distance_to_stop_loss:.2f}%")
        logging.info(f"  Distance to take-profit: {distance_to_take_profit:.2f}%")

        return "HOLD", f"Current P&L: {pnl_pct:.2f}% {pnl_status}"

    except Exception as e:
        logging.error(f"Error checking risk rules: {e}")
        return "HOLD", f"Error in risk check: {str(e)}"

def check_time_based_exit(unlock_date_str: str) -> str:
    """
    Check if we should exit based on time proximity to unlock date
    """
    try:
        # Parse unlock date
        if unlock_date_str.endswith('Z'):
            unlock_date_str = unlock_date_str[:-1] + '+00:00'
        
        unlock_date = datetime.fromisoformat(unlock_date_str)
        if unlock_date.tzinfo is None:
            unlock_date = unlock_date.replace(tzinfo=timezone.utc)
        
        # Calculate exit date (X days before unlock)
        take_profit_date = unlock_date - timedelta(days=TAKE_PROFIT_DAYS_BEFORE_UNLOCK)
        current_time = datetime.now(timezone.utc)
        
        logging.info(f"  Unlock date: {unlock_date}")
        logging.info(f"  Exit date: {take_profit_date}")
        logging.info(f"  Current time: {current_time}")
        
        if current_time >= take_profit_date:
            days_to_unlock = (unlock_date - current_time).days
            return f"Time-based exit triggered ({days_to_unlock} days to unlock)"
        
        return None
        
    except Exception as e:
        logging.error(f"Error in time-based exit check: {e}")
        return None

def calculate_position_metrics(position: Dict[str, Any], current_price: Decimal) -> Dict[str, Any]:
    """
    Calculate detailed metrics for a position
    """
    try:
        entry_price = Decimal(str(position['entry_price_in_usdc']))
        amount_shorted = Decimal(str(position.get('amount_shorted', 0)))
        
        # P&L calculations
        price_change = current_price - entry_price
        price_change_pct = (price_change / entry_price) * 100
        
        # For shorts: profit when price goes down, loss when price goes up
        pnl_pct = -price_change_pct  # Negative of price change for shorts
        pnl_usd = -price_change * amount_shorted  # Dollar P&L
        
        # Risk metrics
        stop_loss_price = entry_price * (1 + STOP_LOSS_PCT)
        take_profit_price = entry_price * (1 - TAKE_PROFIT_PCT)
        
        # Distance to risk levels
        distance_to_stop_loss = ((stop_loss_price - current_price) / current_price) * 100
        distance_to_take_profit = ((current_price - take_profit_price) / current_price) * 100
        
        return {
            "entry_price": float(entry_price),
            "current_price": float(current_price),
            "price_change_pct": float(price_change_pct),
            "pnl_pct": float(pnl_pct),
            "pnl_usd": float(pnl_usd),
            "stop_loss_price": float(stop_loss_price),
            "take_profit_price": float(take_profit_price),
            "distance_to_stop_loss_pct": float(distance_to_stop_loss),
            "distance_to_take_profit_pct": float(distance_to_take_profit),
            "amount_shorted": float(amount_shorted)
        }
        
    except Exception as e:
        logging.error(f"Error calculating position metrics: {e}")
        return {}

def get_risk_summary(positions: list, prices: dict) -> Dict[str, Any]:
    """
    Get a summary of risk across all positions
    """
    try:
        if not positions:
            return {"total_positions": 0, "total_pnl_usd": 0, "positions_at_risk": 0}
        
        total_pnl_usd = 0
        positions_at_risk = 0
        position_details = []
        
        for pos in positions:
            token_address = pos.get('token_address')
            current_price = prices.get(token_address)
            
            if current_price:
                metrics = calculate_position_metrics(pos, Decimal(str(current_price)))
                total_pnl_usd += metrics.get('pnl_usd', 0)
                
                # Check if position is at risk (close to stop loss)
                distance_to_stop = metrics.get('distance_to_stop_loss_pct', 100)
                if distance_to_stop < 20:  # Within 20% of stop loss
                    positions_at_risk += 1
                
                position_details.append({
                    "position_id": pos.get('position_id'),
                    "token_symbol": pos.get('token_symbol'),
                    "metrics": metrics
                })
        
        return {
            "total_positions": len(positions),
            "total_pnl_usd": total_pnl_usd,
            "positions_at_risk": positions_at_risk,
            "position_details": position_details
        }
        
    except Exception as e:
        logging.error(f"Error calculating risk summary: {e}")
        return {"error": str(e)}
