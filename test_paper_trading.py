#!/usr/bin/env python3
"""
Comprehensive test script for Project Chimera Paper Trading
Simulates the complete trading workflow without real transactions
"""

import os
import sys
import json
import time
from pathlib import Path
from decimal import Decimal
from datetime import datetime, timedelta

# Set paper trading mode and test environment
os.environ['PAPER_TRADING_MODE'] = 'true'

# Set mock Redis URL for testing (prevents Redis connection errors in tests)
if not os.environ.get('REDIS_URL'):
    os.environ['REDIS_URL'] = 'redis://localhost:6379'  # Mock Redis URL for testing

# Set other required environment variables for testing
os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.75')
os.environ.setdefault('STOP_LOSS_PCT', '0.15')
os.environ.setdefault('TAKE_PROFIT_PCT', '0.10')
os.environ.setdefault('BORROW_AMOUNT_PER_TRADE', '1000')

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-executioner'))
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-seer'))
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def test_paper_trading_engine():
    """Test the paper trading engine directly"""
    print("🧪 Testing Paper Trading Engine...")

    try:
        from paper_trading import paper_engine, execute_paper_trade, close_paper_position

        # Generate realistic future unlock date (30 days from now)
        future_unlock_date = (datetime.now() + timedelta(days=30)).isoformat() + 'Z'

        # Test candidate with future unlock date
        test_candidate = {
            'token_symbol': 'DYDX',
            'contract_address': '0x92D6C1e31e14519D225d5829CF70AF773944c7f',
            'unlock_date': future_unlock_date,
            'pressure_score': 2.45,
            'unlock_amount': 150000000,
            'circulating_supply': 300000000
        }
        
        print(f"📊 Initial Portfolio: {paper_engine.get_portfolio_summary()}")
        
        # Execute paper trade
        print(f"\n🎯 Executing paper trade for {test_candidate['token_symbol']}...")
        position = execute_paper_trade(test_candidate)
        
        print(f"✅ Position opened: ID {position['position_id']}")
        print(f"💰 Entry price: ${position['entry_price_in_usdc']:.4f}")
        
        # Simulate price movement and close position
        print(f"\n📈 Simulating price movement...")
        new_price = Decimal('2.20')  # 12% profit for short position
        closed_position = close_paper_position(
            position['position_id'], 
            "Take-Profit triggered (test)", 
            new_price
        )
        
        print(f"✅ Position closed with P&L: ${closed_position.get('pnl_usd', 0):.2f}")
        
        # Show final portfolio
        final_portfolio = paper_engine.get_portfolio_summary()
        print(f"\n📊 Final Portfolio: {final_portfolio}")
        
        return True
        
    except Exception as e:
        print(f"❌ Paper trading engine test failed: {e}")
        return False

def test_executioner_paper_mode():
    """Test The Executioner in paper trading mode"""
    print("\n⚔️ Testing The Executioner in Paper Trading Mode...")

    try:
        # Import from the executioner service specifically
        import sys
        from pathlib import Path
        executioner_path = Path(__file__).parent / 'services' / 'the-executioner'
        sys.path.insert(0, str(executioner_path))

        from main import execute_short_trade, PAPER_TRADING_MODE
        
        print(f"🧪 Paper trading mode: {PAPER_TRADING_MODE}")
        
        # Generate realistic future unlock date (45 days from now)
        future_unlock_date = (datetime.now() + timedelta(days=45)).isoformat() + 'Z'

        # Test candidate with future unlock date
        test_candidate = {
            'token_symbol': 'UNI',
            'contract_address': '0x1f9840a85d5aF5bf1D1762F925BDADdC4201F984',
            'unlock_date': future_unlock_date,
            'pressure_score': 1.85,
            'unlock_amount': 83333333,
            'circulating_supply': 750000000
        }
        
        print(f"🎯 Sending trade candidate to Executioner: {test_candidate['token_symbol']}")
        execute_short_trade(test_candidate)
        
        print("✅ Executioner paper trade completed")
        return True
        
    except Exception as e:
        print(f"❌ Executioner paper mode test failed: {e}")
        return False

def test_seer_analysis():
    """Test The Seer analysis functions"""
    print("\n🧠 Testing The Seer Analysis...")
    
    try:
        from analysis import calculate_unlock_pressure_score, calculate_risk_metrics
        
        test_event = {
            'token_symbol': 'AAVE',
            'unlock_amount': 25000000,
            'circulating_supply': 14000000,
            'total_supply': 16000000
        }
        
        # Calculate pressure score
        pressure_score = calculate_unlock_pressure_score(test_event)
        print(f"📊 Pressure score for {test_event['token_symbol']}: {pressure_score:.4f}")
        
        # Calculate risk metrics
        risk_metrics = calculate_risk_metrics(test_event)
        print(f"⚠️  Risk metrics: {risk_metrics}")
        
        # Check if it would be a trade candidate
        threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
        is_candidate = pressure_score > threshold
        
        print(f"🎯 Trade candidate (score > {threshold}): {is_candidate}")
        
        return True
        
    except Exception as e:
        print(f"❌ Seer analysis test failed: {e}")
        return False

def test_ledger_risk_management():
    """Test The Ledger risk management with paper positions"""
    print("\n📊 Testing The Ledger Risk Management...")
    
    try:
        from risk_manager import check_risk_rules, calculate_position_metrics
        
        # Generate realistic future unlock date (15 days from now)
        future_unlock_date = (datetime.now() + timedelta(days=15)).isoformat() + 'Z'

        # Create test position with future unlock date
        test_position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.50',
            'unlock_date': future_unlock_date,
            'amount_shorted': '1000'
        }
        
        # Test different price scenarios
        scenarios = [
            (Decimal('2.40'), "Small profit expected"),
            (Decimal('2.75'), "Stop loss should trigger"),
            (Decimal('2.25'), "Take profit should trigger"),
            (Decimal('2.48'), "Should hold position")
        ]
        
        for price, description in scenarios:
            action, reason = check_risk_rules(test_position, price)
            metrics = calculate_position_metrics(test_position, price)
            
            print(f"💱 Price ${price}: {action} - {reason}")
            print(f"   P&L: {metrics.get('pnl_pct', 0):.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Ledger risk management test failed: {e}")
        return False

def test_complete_workflow():
    """Test the complete paper trading workflow"""
    print("\n🔄 Testing Complete Paper Trading Workflow...")
    
    try:
        from paper_trading import paper_engine
        
        # Generate realistic future unlock dates
        future_unlock_date = (datetime.now() + timedelta(days=20)).isoformat() + 'Z'

        # Simulate Oracle -> Seer -> Executioner workflow
        unlock_events = [
            {
                'token_symbol': 'COMP',
                'contract_address': '0xc00e94Cb662C3520282E6f5717214004A7f26888',
                'unlock_date': future_unlock_date,
                'unlock_amount': 50000000,
                'circulating_supply': 100000000,
                'total_supply': 10000000
            }
        ]
        
        print("🔮 Oracle: Simulating unlock event detection...")
        for event in unlock_events:
            print(f"   Found unlock: {event['token_symbol']} on {event['unlock_date']}")
        
        print("\n🧠 Seer: Analyzing unlock events...")
        from analysis import calculate_unlock_pressure_score
        
        trade_candidates = []
        for event in unlock_events:
            score = calculate_unlock_pressure_score(event)
            threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.75'))
            
            if score > threshold:
                candidate = {**event, 'pressure_score': score}
                trade_candidates.append(candidate)
                print(f"   ✅ Trade candidate: {event['token_symbol']} (score: {score:.4f})")
            else:
                print(f"   ❌ Below threshold: {event['token_symbol']} (score: {score:.4f})")
        
        print(f"\n⚔️ Executioner: Processing {len(trade_candidates)} candidates...")
        from paper_trading import execute_paper_trade
        
        positions = []
        for candidate in trade_candidates:
            position = execute_paper_trade(candidate)
            positions.append(position)
            print(f"   📈 Opened position: {candidate['token_symbol']} at ${position['entry_price_in_usdc']:.4f}")
        
        print(f"\n📊 Ledger: Monitoring {len(positions)} positions...")
        from risk_manager import check_risk_rules
        from price_fetcher import get_realtime_price
        
        for position in positions:
            # Simulate price check
            current_price = Decimal('2.30')  # Mock current price
            action, reason = check_risk_rules(position, current_price)
            print(f"   🔍 {position['token_symbol']}: {action} - {reason}")
        
        # Show final portfolio summary
        portfolio = paper_engine.get_portfolio_summary()
        print(f"\n💰 Final Portfolio Summary:")
        print(f"   Open positions: {portfolio['positions']['open']}")
        print(f"   Closed positions: {portfolio['positions']['closed']}")
        print(f"   Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
        print(f"   Win rate: {portfolio['performance']['winning_trades']}/{portfolio['performance']['total_trades']}")
        
        # Save trading log
        log_file = paper_engine.save_trading_log()
        print(f"📄 Trading log saved: {log_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ Complete workflow test failed: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Project Chimera - Paper Trading Test Suite")
    print("=" * 60)
    
    success = True
    
    # Run all tests
    success &= test_paper_trading_engine()
    success &= test_executioner_paper_mode()
    success &= test_seer_analysis()
    success &= test_ledger_risk_management()
    success &= test_complete_workflow()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 ALL PAPER TRADING TESTS PASSED!")
        print("\n✅ **Paper Trading System Status**: FULLY OPERATIONAL")
        print("\n📋 **What Was Tested**:")
        print("   • Paper trading engine simulation")
        print("   • Executioner paper mode execution")
        print("   • Seer analysis and scoring")
        print("   • Ledger risk management")
        print("   • Complete end-to-end workflow")
        
        print("\n🎯 **Ready For**:")
        print("   • Safe strategy testing without real money")
        print("   • Parameter optimization and backtesting")
        print("   • Risk management validation")
        print("   • Performance analysis and reporting")
        
        print("\n💡 **Next Steps**:")
        print("   1. Run paper trading for several days")
        print("   2. Analyze performance and optimize parameters")
        print("   3. When satisfied, switch to live trading")
        print("   4. Start with small amounts on mainnet")
        
    else:
        print("❌ Some paper trading tests failed.")
        print("Please review the errors above.")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
