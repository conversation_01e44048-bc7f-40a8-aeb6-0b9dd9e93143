import requests
import logging
from decimal import Decimal
from typing import Optional, Dict, Any
import time

# CoinGecko API configuration
COINGECKO_API_BASE = "https://api.coingecko.com/api/v3"
REQUEST_TIMEOUT = 10
RATE_LIMIT_DELAY = 1.1  # Delay between requests to respect rate limits

# Cache for price data to avoid excessive API calls
price_cache = {}
cache_duration = 60  # Cache prices for 60 seconds

def get_realtime_price(token_address: str) -> Optional[Decimal]:
    """
    Enhanced real-time price fetching with 2025 best practices.

    Priority order:
    1. Binance REST API (fast, reliable, no API key needed)
    2. Binance WebSocket (real-time, for high-frequency needs)
    3. CoinGecko API (comprehensive fallback with caching)

    This ensures maximum reliability and speed for trading decisions.
    """
    try:
        # PRIORITY 1: Try Binance REST API (fast, reliable, no API key)
        try:
            from binance_simple import get_binance_price
            binance_price = get_binance_price(token_address)
            if binance_price:
                logging.info(f"💰 Binance REST API price for {token_address}: ${binance_price:.4f}")
                return binance_price
        except Exception as e:
            logging.warning(f"⚠️ Binance REST API unavailable: {e}")

        # PRIORITY 2: Try Binance WebSocket for real-time data
        try:
            from binance_websocket import get_realtime_price_binance
            binance_ws_price = get_realtime_price_binance(token_address)
            if binance_ws_price:
                logging.info(f"📡 Binance WebSocket price for {token_address}: ${binance_ws_price:.4f}")
                return binance_ws_price
        except Exception as e:
            logging.warning(f"⚠️ Binance WebSocket unavailable: {e}")

        # PRIORITY 3: Check CoinGecko cache
        cache_key = token_address.lower()
        current_time = time.time()

        if cache_key in price_cache:
            cached_data = price_cache[cache_key]
            if current_time - cached_data['timestamp'] < cache_duration:
                logging.info(f"📊 Using cached CoinGecko price for {token_address}: ${cached_data['price']:.4f}")
                return cached_data['price']
        
        # Fetch from API
        url = f"{COINGECKO_API_BASE}/simple/token_price/ethereum"
        params = {
            "contract_addresses": token_address,
            "vs_currencies": "usd"
        }
        
        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        
        # Extract price
        token_data = data.get(token_address.lower(), {})
        price_usd = token_data.get('usd')
        
        if price_usd is not None:
            price = Decimal(str(price_usd))
            
            # Cache the result
            price_cache[cache_key] = {
                'price': price,
                'timestamp': current_time
            }
            
            logging.info(f"Fetched price for {token_address}: ${price:.4f}")
            
            # Rate limiting
            time.sleep(RATE_LIMIT_DELAY)
            
            return price
        else:
            logging.warning(f"No price data found for token {token_address}")
            return None
            
    except requests.RequestException as e:
        logging.error(f"Error fetching price for {token_address}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error fetching price for {token_address}: {e}")
        return None

def get_multiple_prices(token_addresses: list) -> Dict[str, Decimal]:
    """
    Fetch prices for multiple tokens in a single API call
    """
    try:
        if not token_addresses:
            return {}
        
        # Filter out cached prices
        current_time = time.time()
        addresses_to_fetch = []
        result = {}
        
        for addr in token_addresses:
            cache_key = addr.lower()
            if cache_key in price_cache:
                cached_data = price_cache[cache_key]
                if current_time - cached_data['timestamp'] < cache_duration:
                    result[addr] = cached_data['price']
                    continue
            addresses_to_fetch.append(addr)
        
        if not addresses_to_fetch:
            return result
        
        # Fetch remaining prices
        url = f"{COINGECKO_API_BASE}/simple/token_price/ethereum"
        params = {
            "contract_addresses": ",".join(addresses_to_fetch),
            "vs_currencies": "usd"
        }
        
        response = requests.get(url, params=params, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        
        # Process results
        for addr in addresses_to_fetch:
            token_data = data.get(addr.lower(), {})
            price_usd = token_data.get('usd')
            
            if price_usd is not None:
                price = Decimal(str(price_usd))
                result[addr] = price
                
                # Cache the result
                price_cache[addr.lower()] = {
                    'price': price,
                    'timestamp': current_time
                }
        
        logging.info(f"Fetched prices for {len(result)} tokens")
        
        # Rate limiting
        time.sleep(RATE_LIMIT_DELAY)
        
        return result
        
    except requests.RequestException as e:
        logging.error(f"Error fetching multiple prices: {e}")
        return {}
    except Exception as e:
        logging.error(f"Unexpected error fetching multiple prices: {e}")
        return {}

def get_token_info(token_address: str) -> Optional[Dict[str, Any]]:
    """
    Get detailed token information including market data
    """
    try:
        url = f"{COINGECKO_API_BASE}/coins/ethereum/contract/{token_address}"
        
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        
        market_data = data.get("market_data", {})
        
        return {
            "symbol": data.get("symbol", "").upper(),
            "name": data.get("name", ""),
            "current_price": market_data.get("current_price", {}).get("usd"),
            "market_cap": market_data.get("market_cap", {}).get("usd"),
            "total_volume": market_data.get("total_volume", {}).get("usd"),
            "circulating_supply": market_data.get("circulating_supply"),
            "total_supply": market_data.get("total_supply"),
            "price_change_24h": market_data.get("price_change_percentage_24h"),
            "price_change_7d": market_data.get("price_change_percentage_7d"),
            "ath": market_data.get("ath", {}).get("usd"),
            "atl": market_data.get("atl", {}).get("usd")
        }
        
    except requests.RequestException as e:
        logging.error(f"Error fetching token info for {token_address}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error fetching token info: {e}")
        return None

def get_price_history(token_address: str, days: int = 7) -> Optional[list]:
    """
    Get historical price data for a token
    """
    try:
        # First get the coin ID from the contract address
        url = f"{COINGECKO_API_BASE}/coins/ethereum/contract/{token_address}"
        response = requests.get(url, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        coin_data = response.json()
        coin_id = coin_data.get("id")
        
        if not coin_id:
            logging.error(f"Could not find coin ID for {token_address}")
            return None
        
        # Get historical data
        history_url = f"{COINGECKO_API_BASE}/coins/{coin_id}/market_chart"
        params = {
            "vs_currency": "usd",
            "days": str(days)
        }
        
        response = requests.get(history_url, params=params, timeout=REQUEST_TIMEOUT)
        response.raise_for_status()
        
        data = response.json()
        prices = data.get("prices", [])
        
        # Convert to more usable format
        price_history = []
        for timestamp, price in prices:
            price_history.append({
                "timestamp": timestamp,
                "price": float(price)
            })
        
        return price_history
        
    except requests.RequestException as e:
        logging.error(f"Error fetching price history for {token_address}: {e}")
        return None
    except Exception as e:
        logging.error(f"Unexpected error fetching price history: {e}")
        return None

def clear_price_cache():
    """Clear the price cache"""
    global price_cache
    price_cache = {}
    logging.info("Price cache cleared")

def get_cache_stats() -> Dict[str, Any]:
    """Get statistics about the price cache"""
    current_time = time.time()
    valid_entries = 0
    expired_entries = 0
    
    for cache_key, cached_data in price_cache.items():
        if current_time - cached_data['timestamp'] < cache_duration:
            valid_entries += 1
        else:
            expired_entries += 1
    
    return {
        "total_entries": len(price_cache),
        "valid_entries": valid_entries,
        "expired_entries": expired_entries,
        "cache_duration": cache_duration
    }
