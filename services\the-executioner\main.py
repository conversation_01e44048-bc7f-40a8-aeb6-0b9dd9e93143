import os
import redis
import json
import logging
from decimal import Decimal

from wallet_manager import get_wallet_and_provider
from lending_handler import borrow_asset
from dex_handler import swap_tokens
from db_handler import log_trade_entry
from paper_trading import execute_paper_trade, paper_engine

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Redis setup
REDIS_URL = os.environ.get("REDIS_URL")

def get_redis_connection():
    """Get Redis connection"""
    if not REDIS_URL:
        raise Exception("REDIS_URL environment variable not set")
    return redis.from_url(REDIS_URL)

TRADE_CANDIDATE_CHANNEL = "chimera:trade_candidates"
POSITION_OPENED_CHANNEL = "chimera:position_opened"

# Trade parameters
BORROW_AMOUNT = Decimal(os.environ.get("BORROW_AMOUNT_PER_TRADE", "1000")) # Amount in terms of the token to borrow
STABLECOIN_ADDRESS = "******************************************" # USDC on Mainnet
PAPER_TRADING_MODE = os.environ.get("PAPER_TRADING_MODE", "true").lower() == "true"

def execute_paper_trade_workflow(candidate: dict):
    """Execute paper trading workflow"""
    try:
        # Execute paper trade
        position = execute_paper_trade(candidate)

        # Publish position opened event
        position_event = {
            "position_id": position['position_id'],
            "token_symbol": position['token_symbol'],
            "token_address": position['token_address'],
            "amount_shorted": str(position['amount_shorted']),
            "entry_price_in_usdc": str(position['entry_price_in_usdc']),
            "unlock_date": position['unlock_date'],
            "status": "OPEN",
            "borrow_tx_hash": position['borrow_tx_hash'],
            "swap_tx_hash": position['swap_tx_hash'],
            "paper_trade": True
        }

        r = get_redis_connection()
        r.publish(POSITION_OPENED_CHANNEL, json.dumps(position_event, default=str))

        # Get portfolio summary
        portfolio = paper_engine.get_portfolio_summary()

        logging.warning(f"✅ PAPER POSITION OPENED: {position['token_symbol']} at ${position['entry_price_in_usdc']:.4f}")
        logging.info(f"📊 Portfolio: {portfolio['positions']['open']} open, {portfolio['positions']['closed']} closed")
        logging.info(f"💰 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")

    except Exception as e:
        logging.error(f"❌ Paper trade execution failed for {candidate.get('token_symbol', 'Unknown')}: {e}")
        # Publish error event
        try:
            error_event = {
                "token_symbol": candidate.get('token_symbol'),
                "token_address": candidate.get('contract_address'),
                "error": str(e),
                "status": "FAILED",
                "paper_trade": True
            }
            r = get_redis_connection()
            r.publish("chimera:execution_errors", json.dumps(error_event))
        except Exception as pub_error:
            logging.error(f"Failed to publish paper trade error event: {pub_error}")

def execute_short_trade(candidate: dict):
    """Executes the complete short-selling sequence for a trade candidate."""
    token_symbol = candidate.get('token_symbol')
    token_address = candidate.get('contract_address')
    unlock_date = candidate.get('unlock_date')

    logging.warning(f"The Executioner received high-conviction candidate: {token_symbol}")

    # Check if paper trading mode is enabled
    if PAPER_TRADING_MODE:
        logging.warning("🧪 PAPER TRADING MODE: Simulating trade execution")
        return execute_paper_trade_workflow(candidate)

    try:
        # 1. Setup wallet and provider
        web3, account = get_wallet_and_provider()
        logging.info(f"Connected to web3 with account: {account.address}")
        
        # 2. Borrow the asset from Aave
        logging.info(f"Attempting to borrow {BORROW_AMOUNT} {token_symbol}...")
        borrow_tx_hash = borrow_asset(web3, account, token_address, BORROW_AMOUNT)
        if not borrow_tx_hash:
            raise Exception("Borrow transaction failed.")
        logging.info(f"Borrow successful. Tx: {borrow_tx_hash}")

        # 3. Swap the borrowed asset for a stablecoin (USDC)
        logging.info(f"Attempting to swap {BORROW_AMOUNT} {token_symbol} for USDC...")
        swap_tx_hash, amount_out = swap_tokens(
            web3=web3,
            account=account,
            from_token=token_address,
            to_token=STABLECOIN_ADDRESS,
            amount=BORROW_AMOUNT
        )
        if not swap_tx_hash:
            raise Exception("DEX swap transaction failed.")
        logging.info(f"Swap successful. Received {amount_out} USDC. Tx: {swap_tx_hash}")

        # 4. Calculate entry price and log the position
        entry_price = amount_out / BORROW_AMOUNT
        position_id = log_trade_entry(
            token_symbol=token_symbol,
            token_address=token_address,
            amount_shorted=float(BORROW_AMOUNT),
            entry_price=float(entry_price),
            unlock_date=unlock_date,
            borrow_tx_hash=borrow_tx_hash,
            swap_tx_hash=swap_tx_hash
        )
        
        # 5. Publish position opened event
        position_event = {
            "position_id": position_id,
            "token_symbol": token_symbol,
            "token_address": token_address,
            "amount_shorted": str(BORROW_AMOUNT),
            "entry_price_in_usdc": str(entry_price),
            "unlock_date": unlock_date,
            "status": "OPEN",
            "borrow_tx_hash": borrow_tx_hash,
            "swap_tx_hash": swap_tx_hash
        }
        
        r = get_redis_connection()
        r.publish(POSITION_OPENED_CHANNEL, json.dumps(position_event, default=str))
        logging.warning(f"SUCCESS: Position opened for {token_symbol}. Published to {POSITION_OPENED_CHANNEL}")

    except Exception as e:
        logging.error(f"Execution failed for {token_symbol}: {e}")
        # Publish error event
        try:
            error_event = {
                "token_symbol": token_symbol,
                "token_address": token_address,
                "error": str(e),
                "status": "FAILED"
            }
            r = get_redis_connection()
            r.publish("chimera:execution_errors", json.dumps(error_event))
        except Exception as pub_error:
            logging.error(f"Failed to publish error event: {pub_error}")

def listen_for_candidates():
    """Main loop to listen for trade candidates from Redis."""
    logging.info("The Executioner is now listening for trade candidates...")
    
    try:
        r = get_redis_connection()
        p = r.pubsub(ignore_subscribe_messages=True)
        p.subscribe(TRADE_CANDIDATE_CHANNEL)
        
        for message in p.listen():
            try:
                if message['type'] == 'message':
                    candidate_data = json.loads(message['data'])
                    execute_short_trade(candidate_data)
            except (json.JSONDecodeError, KeyError) as e:
                logging.error(f"Failed to process message: {message}. Error: {e}")
            except Exception as e:
                logging.error(f"Unexpected error processing candidate: {e}")
                
    except Exception as e:
        logging.error(f"Error in main listening loop: {e}")
        raise

if __name__ == "__main__":
    listen_for_candidates()
