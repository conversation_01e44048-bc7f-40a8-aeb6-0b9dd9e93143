#!/usr/bin/env python3
"""
Local development setup script for Project Chimera
Sets up the development environment and runs basic checks
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def run_command(cmd, cwd=None, check=True):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    
    if result.stdout:
        print(result.stdout)
    if result.stderr and result.returncode != 0:
        print(result.stderr, file=sys.stderr)
    
    if check and result.returncode != 0:
        raise subprocess.CalledProcessError(result.returncode, cmd)
    
    return result

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    version = sys.version_info
    
    if version.major != 3 or version.minor < 10:
        print(f"❌ Python 3.10+ required, found {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro}")
    return True

def check_dependencies():
    """Check if required dependencies are available"""
    print("\n📦 Checking dependencies...")
    
    required_packages = [
        'redis', 'psycopg2-binary', 'requests', 'web3', 'eth-account'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n📥 Installing missing packages: {', '.join(missing_packages)}")
        cmd = [sys.executable, '-m', 'pip', 'install'] + missing_packages
        try:
            run_command(cmd)
            print("✅ Dependencies installed successfully")
        except subprocess.CalledProcessError:
            print("❌ Failed to install dependencies")
            return False
    
    return True

def setup_environment_file():
    """Create a sample .env file"""
    print("\n🔧 Setting up environment file...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if env_file.exists():
        print("✅ .env file already exists")
        return True
    
    # Create example environment file
    env_content = """# Project Chimera Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration
DATABASE_URL=postgresql://user:password@localhost:5432/chimera_db
REDIS_URL=redis://localhost:6379

# Blockchain Configuration
INFURA_API_KEY=your_infura_api_key_here
PRIVATE_KEY_PATH=/path/to/your/private/key/file

# Data Source APIs
TOKENUNLOCKS_API_KEY=your_tokenunlocks_api_key
VESTLAB_API_KEY=your_vestlab_api_key

# Telegram Notifications
TELEGRAM_BOT_TOKEN=your_telegram_bot_token
TELEGRAM_CHAT_ID=your_telegram_chat_id

# Risk Management Parameters
PRESSURE_SCORE_THRESHOLD=0.75
STOP_LOSS_PCT=0.15
TAKE_PROFIT_PCT=0.10
TAKE_PROFIT_DAYS_BEFORE_UNLOCK=1
BORROW_AMOUNT_PER_TRADE=1000
MONITORING_INTERVAL_SECONDS=60

# Development Settings
PYTHONPATH=.
LOGGING_LEVEL=INFO
"""
    
    with open(env_example, 'w') as f:
        f.write(env_content)
    
    print("✅ Created .env.example file")
    print("📝 Please copy .env.example to .env and fill in your actual values")
    
    return True

def check_database_connection():
    """Check database connection"""
    print("\n🗄️  Checking database connection...")
    
    db_url = os.environ.get('DATABASE_URL')
    if not db_url:
        print("⚠️  DATABASE_URL not set, skipping database check")
        return True
    
    try:
        import psycopg2
        conn = psycopg2.connect(db_url)
        
        with conn.cursor() as cur:
            cur.execute("SELECT version()")
            version = cur.fetchone()[0]
            print(f"✅ Database connected: {version}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def check_redis_connection():
    """Check Redis connection"""
    print("\n🔴 Checking Redis connection...")
    
    redis_url = os.environ.get('REDIS_URL')
    if not redis_url:
        print("⚠️  REDIS_URL not set, skipping Redis check")
        return True
    
    try:
        import redis
        r = redis.from_url(redis_url)
        r.ping()
        print("✅ Redis connected")
        return True
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        return False

def run_basic_tests():
    """Run basic import tests"""
    print("\n🧪 Running basic import tests...")
    
    services = ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']
    
    for service in services:
        service_path = Path(f'services/{service}')
        if service_path.exists():
            try:
                sys.path.insert(0, str(service_path))
                
                # Try to import main module
                import importlib.util
                spec = importlib.util.spec_from_file_location("main", service_path / "main.py")
                if spec and spec.loader:
                    module = importlib.util.module_from_spec(spec)
                    # Don't execute, just check syntax
                    print(f"✅ {service} syntax OK")
                else:
                    print(f"❌ {service} main.py not found")
                
                sys.path.remove(str(service_path))
                
            except Exception as e:
                print(f"❌ {service} import failed: {e}")
                if str(service_path) in sys.path:
                    sys.path.remove(str(service_path))
        else:
            print(f"⚠️  Service {service} not found")

def create_project_structure():
    """Ensure project structure is correct"""
    print("\n📁 Checking project structure...")
    
    required_dirs = [
        'services/the-oracle',
        'services/the-seer', 
        'services/the-executioner',
        'services/the-ledger',
        'services/the-herald',
        'database',
        'tests',
        'scripts'
    ]
    
    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✅ {dir_path}")
    
    # Create __init__.py files for Python packages
    for service_dir in Path('services').iterdir():
        if service_dir.is_dir():
            init_file = service_dir / '__init__.py'
            if not init_file.exists():
                init_file.touch()

def setup_git_hooks():
    """Set up git hooks for development"""
    print("\n🔗 Setting up git hooks...")
    
    if not Path('.git').exists():
        print("⚠️  Not a git repository, skipping git hooks")
        return
    
    hooks_dir = Path('.git/hooks')
    pre_commit_hook = hooks_dir / 'pre-commit'
    
    if pre_commit_hook.exists():
        print("✅ Pre-commit hook already exists")
        return
    
    hook_content = """#!/bin/bash
# Pre-commit hook for Project Chimera

echo "Running pre-commit checks..."

# Run tests
python scripts/run_tests.py --unit --quality

if [ $? -ne 0 ]; then
    echo "❌ Pre-commit checks failed!"
    exit 1
fi

echo "✅ Pre-commit checks passed!"
"""
    
    with open(pre_commit_hook, 'w') as f:
        f.write(hook_content)
    
    # Make executable
    os.chmod(pre_commit_hook, 0o755)
    print("✅ Pre-commit hook installed")

def generate_requirements():
    """Generate requirements.txt files"""
    print("\n📋 Generating requirements files...")
    
    # Main requirements
    main_requirements = [
        'redis==4.6.0',
        'psycopg2-binary==2.9.7',
        'requests==2.31.0',
        'web3==6.11.0',
        'eth-account==0.9.0'
    ]
    
    # Development requirements
    dev_requirements = [
        'pytest==7.4.0',
        'pytest-cov==4.1.0',
        'black==23.7.0',
        'flake8==6.0.0',
        'mypy==1.5.0'
    ]
    
    with open('requirements.txt', 'w') as f:
        f.write('\n'.join(main_requirements))
    
    with open('requirements-dev.txt', 'w') as f:
        f.write('\n'.join(dev_requirements))
    
    print("✅ Requirements files generated")

def main():
    """Main setup function"""
    print("🚀 Setting up Project Chimera development environment...\n")
    
    success = True
    
    # Basic checks
    success &= check_python_version()
    
    # Project structure
    create_project_structure()
    
    # Dependencies
    success &= check_dependencies()
    
    # Environment setup
    setup_environment_file()
    generate_requirements()
    
    # Load environment if .env exists
    env_file = Path('.env')
    if env_file.exists():
        print("\n🔧 Loading environment from .env file...")
        with open(env_file) as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value
    
    # External service checks
    check_database_connection()
    check_redis_connection()
    
    # Code checks
    run_basic_tests()
    
    # Development tools
    setup_git_hooks()
    
    print("\n" + "="*50)
    print("SETUP SUMMARY")
    print("="*50)
    
    if success:
        print("🎉 Development environment setup completed!")
        print("\n📝 Next steps:")
        print("1. Copy .env.example to .env and fill in your values")
        print("2. Set up your PostgreSQL and Redis instances")
        print("3. Run 'python database/init_db.py init' to set up the database")
        print("4. Run 'python scripts/run_tests.py' to verify everything works")
        print("5. Start developing! 🚀")
    else:
        print("❌ Setup completed with some issues. Please review the output above.")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
