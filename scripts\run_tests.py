#!/usr/bin/env python3
"""
Test runner script for Project Chimera
Runs all tests and generates coverage reports
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path

def run_command(cmd, cwd=None):
    """Run a command and return the result"""
    print(f"Running: {' '.join(cmd)}")
    result = subprocess.run(cmd, cwd=cwd, capture_output=True, text=True)
    
    if result.stdout:
        print(result.stdout)
    if result.stderr:
        print(result.stderr, file=sys.stderr)
    
    return result.returncode == 0

def setup_test_environment():
    """Set up environment variables for testing"""
    test_env = {
        'PRESSURE_SCORE_THRESHOLD': '0.75',
        'STOP_LOSS_PCT': '0.15',
        'TAKE_PROFIT_PCT': '0.10',
        'TAKE_PROFIT_DAYS_BEFORE_UNLOCK': '1',
        'BORROW_AMOUNT_PER_TRADE': '1000',
        'MONITORING_INTERVAL_SECONDS': '60',
        'PYTHONPATH': str(Path(__file__).parent.parent)
    }
    
    for key, value in test_env.items():
        os.environ.setdefault(key, value)
    
    print("Test environment configured")

def run_unit_tests(verbose=False):
    """Run unit tests"""
    print("\n" + "="*50)
    print("RUNNING UNIT TESTS")
    print("="*50)
    
    test_files = [
        'tests/test_oracle.py',
        'tests/test_seer.py', 
        'tests/test_ledger.py'
    ]
    
    all_passed = True
    
    for test_file in test_files:
        if Path(test_file).exists():
            print(f"\nRunning {test_file}...")
            cmd = [sys.executable, '-m', 'unittest', test_file.replace('/', '.').replace('.py', '')]
            if verbose:
                cmd.append('-v')
            
            success = run_command(cmd)
            if not success:
                all_passed = False
                print(f"❌ {test_file} FAILED")
            else:
                print(f"✅ {test_file} PASSED")
        else:
            print(f"⚠️  {test_file} not found")
    
    return all_passed

def run_integration_tests(verbose=False):
    """Run integration tests"""
    print("\n" + "="*50)
    print("RUNNING INTEGRATION TESTS")
    print("="*50)
    
    test_file = 'tests/test_integration.py'
    
    if Path(test_file).exists():
        print(f"\nRunning {test_file}...")
        cmd = [sys.executable, '-m', 'unittest', test_file.replace('/', '.').replace('.py', '')]
        if verbose:
            cmd.append('-v')
        
        success = run_command(cmd)
        if success:
            print(f"✅ {test_file} PASSED")
        else:
            print(f"❌ {test_file} FAILED")
        
        return success
    else:
        print(f"⚠️  {test_file} not found")
        return False

def run_pytest_with_coverage():
    """Run tests with pytest and coverage"""
    print("\n" + "="*50)
    print("RUNNING PYTEST WITH COVERAGE")
    print("="*50)
    
    # Check if pytest is available
    try:
        import pytest
        import coverage
    except ImportError:
        print("❌ pytest or coverage not installed. Install with:")
        print("pip install pytest pytest-cov")
        return False
    
    cmd = [
        sys.executable, '-m', 'pytest', 
        'tests/', 
        '--cov=services',
        '--cov-report=term-missing',
        '--cov-report=html:htmlcov',
        '-v'
    ]
    
    success = run_command(cmd)
    
    if success:
        print("✅ Pytest with coverage completed")
        print("📊 Coverage report generated in htmlcov/index.html")
    else:
        print("❌ Pytest with coverage failed")
    
    return success

def run_service_tests():
    """Run individual service tests"""
    print("\n" + "="*50)
    print("RUNNING SERVICE-SPECIFIC TESTS")
    print("="*50)
    
    services = ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']
    
    for service in services:
        service_path = Path(f'services/{service}')
        if service_path.exists():
            print(f"\n🔍 Testing {service}...")
            
            # Check if service has its own tests
            service_test_path = service_path / 'tests'
            if service_test_path.exists():
                cmd = [sys.executable, '-m', 'unittest', 'discover', '-s', str(service_test_path)]
                success = run_command(cmd, cwd=service_path)
                if success:
                    print(f"✅ {service} tests passed")
                else:
                    print(f"❌ {service} tests failed")
            else:
                # Try to import the main module to check for syntax errors
                try:
                    sys.path.insert(0, str(service_path))
                    import main
                    print(f"✅ {service} imports successfully")
                    sys.path.remove(str(service_path))
                except Exception as e:
                    print(f"❌ {service} import failed: {e}")
        else:
            print(f"⚠️  Service {service} not found")

def check_code_quality():
    """Run code quality checks"""
    print("\n" + "="*50)
    print("RUNNING CODE QUALITY CHECKS")
    print("="*50)
    
    # Check for common issues
    issues_found = []
    
    # Check for TODO/FIXME comments
    print("🔍 Checking for TODO/FIXME comments...")
    for py_file in Path('.').rglob('*.py'):
        if 'venv' in str(py_file) or '__pycache__' in str(py_file):
            continue
        
        with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                if 'TODO' in line or 'FIXME' in line:
                    issues_found.append(f"{py_file}:{i}: {line.strip()}")
    
    if issues_found:
        print(f"⚠️  Found {len(issues_found)} TODO/FIXME comments:")
        for issue in issues_found[:10]:  # Show first 10
            print(f"  {issue}")
        if len(issues_found) > 10:
            print(f"  ... and {len(issues_found) - 10} more")
    else:
        print("✅ No TODO/FIXME comments found")
    
    # Check for print statements (should use logging)
    print("\n🔍 Checking for print statements...")
    print_statements = []
    
    for py_file in Path('.').rglob('*.py'):
        if 'venv' in str(py_file) or '__pycache__' in str(py_file) or 'tests' in str(py_file):
            continue
        
        with open(py_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
            lines = content.split('\n')
            
            for i, line in enumerate(lines, 1):
                if 'print(' in line and not line.strip().startswith('#'):
                    print_statements.append(f"{py_file}:{i}: {line.strip()}")
    
    if print_statements:
        print(f"⚠️  Found {len(print_statements)} print statements (consider using logging):")
        for stmt in print_statements[:5]:  # Show first 5
            print(f"  {stmt}")
        if len(print_statements) > 5:
            print(f"  ... and {len(print_statements) - 5} more")
    else:
        print("✅ No print statements found in main code")

def main():
    """Main test runner"""
    parser = argparse.ArgumentParser(description='Run Project Chimera tests')
    parser.add_argument('--unit', action='store_true', help='Run unit tests only')
    parser.add_argument('--integration', action='store_true', help='Run integration tests only')
    parser.add_argument('--coverage', action='store_true', help='Run with coverage')
    parser.add_argument('--quality', action='store_true', help='Run code quality checks')
    parser.add_argument('--verbose', '-v', action='store_true', help='Verbose output')
    parser.add_argument('--all', action='store_true', help='Run all tests and checks')
    
    args = parser.parse_args()
    
    # Set up test environment
    setup_test_environment()
    
    success = True
    
    if args.all or (not any([args.unit, args.integration, args.coverage, args.quality])):
        # Run everything by default
        print("🚀 Running complete test suite...")
        
        success &= run_unit_tests(args.verbose)
        success &= run_integration_tests(args.verbose)
        run_service_tests()
        
        if Path('requirements.txt').exists():
            success &= run_pytest_with_coverage()
        
        check_code_quality()
        
    else:
        if args.unit:
            success &= run_unit_tests(args.verbose)
        
        if args.integration:
            success &= run_integration_tests(args.verbose)
        
        if args.coverage:
            success &= run_pytest_with_coverage()
        
        if args.quality:
            check_code_quality()
    
    # Final summary
    print("\n" + "="*50)
    print("TEST SUMMARY")
    print("="*50)
    
    if success:
        print("🎉 All tests passed!")
        sys.exit(0)
    else:
        print("❌ Some tests failed!")
        sys.exit(1)

if __name__ == '__main__':
    main()
