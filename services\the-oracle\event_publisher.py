import os
import redis
import json
from typing import Dict, Any
import logging

# Redis connection details from <PERSON><PERSON>'s environment
REDIS_URL = os.environ.get("REDIS_URL")

def get_redis_connection():
    """Get Redis connection"""
    if not REDIS_URL:
        raise Exception("REDIS_URL environment variable not set")
    return redis.from_url(REDIS_URL)

UNLOCK_EVENT_CHANNEL = "chimera:unlock_events"

def publish_unlock_event(event: Dict[str, Any]):
    """Publishes a token unlock event to the Redis channel."""
    try:
        r = get_redis_connection()
        
        # Convert datetime objects to strings for JSON serialization
        event_copy = event.copy()
        if 'unlock_date' in event_copy and hasattr(event_copy['unlock_date'], 'isoformat'):
            event_copy['unlock_date'] = event_copy['unlock_date'].isoformat()
        
        message = json.dumps(event_copy, default=str)
        r.publish(UNLOCK_EVENT_CHANNEL, message)
        logging.info(f"Published to {UNLOCK_EVENT_CHANNEL}: {event_copy.get('token_symbol', 'Unknown')}")
    except Exception as e:
        logging.error(f"Error publishing unlock event: {e}")
        raise

def publish_position_opened(position_data: Dict[str, Any]):
    """Publishes a position opened event"""
    try:
        r = get_redis_connection()
        message = json.dumps(position_data, default=str)
        r.publish("chimera:position_opened", message)
        logging.info(f"Published position opened: {position_data.get('token_symbol', 'Unknown')}")
    except Exception as e:
        logging.error(f"Error publishing position opened event: {e}")
        raise

def publish_close_position(position_data: Dict[str, Any]):
    """Publishes a close position event"""
    try:
        r = get_redis_connection()
        message = json.dumps(position_data, default=str)
        r.publish("chimera:close_position", message)
        logging.info(f"Published close position: {position_data.get('position_id', 'Unknown')}")
    except Exception as e:
        logging.error(f"Error publishing close position event: {e}")
        raise

def publish_trade_candidate(candidate_data: Dict[str, Any]):
    """Publishes a trade candidate event"""
    try:
        r = get_redis_connection()
        message = json.dumps(candidate_data, default=str)
        r.publish("chimera:trade_candidates", message)
        logging.info(f"Published trade candidate: {candidate_data.get('token_symbol', 'Unknown')}")
    except Exception as e:
        logging.error(f"Error publishing trade candidate event: {e}")
        raise
