import os
import psycopg2
import psycopg2.extras
from typing import Dict, Any, List
from datetime import datetime, timedelta
import logging

# Database connection details from <PERSON><PERSON>'s environment
DB_URL = os.environ.get("DATABASE_URL")

def _get_db_conn():
    """Get database connection"""
    if not DB_URL:
        raise Exception("DATABASE_URL environment variable not set")
    return psycopg2.connect(DB_URL)

def init_database():
    """Initialize database tables if they don't exist"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            # Create unlock_events table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS unlock_events (
                    id SERIAL PRIMARY KEY,
                    token_symbol VARCHAR(20) NOT NULL,
                    contract_address VARCHAR(42) NOT NULL,
                    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
                    unlock_amount DECIMAL NOT NULL,
                    circulating_supply DECIMAL,
                    total_supply DECIMAL,
                    source VARCHAR(50),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    UNIQUE(contract_address, unlock_date)
                )
            """)
            
            # Create positions table
            cur.execute("""
                CREATE TABLE IF NOT EXISTS positions (
                    id SERIAL PRIMARY KEY,
                    token_symbol VARCHAR(20) NOT NULL,
                    token_address VARCHAR(42) NOT NULL,
                    amount_shorted DECIMAL NOT NULL,
                    entry_price_in_usdc DECIMAL NOT NULL,
                    unlock_date TIMESTAMP WITH TIME ZONE NOT NULL,
                    status VARCHAR(20) DEFAULT 'OPEN',
                    borrow_tx_hash VARCHAR(66),
                    swap_tx_hash VARCHAR(66),
                    close_tx_hash VARCHAR(66),
                    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                    closed_at TIMESTAMP WITH TIME ZONE
                )
            """)
            
            # Create indexes
            cur.execute("CREATE INDEX IF NOT EXISTS idx_unlock_events_date ON unlock_events(unlock_date)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_positions_status ON positions(status)")
            cur.execute("CREATE INDEX IF NOT EXISTS idx_positions_token ON positions(token_address)")
            
            conn.commit()
            logging.info("Database tables initialized successfully")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error initializing database: {e}")
        raise
    finally:
        conn.close()

def store_unlock_events(events: List[Dict[str, Any]]):
    """Stores a list of unlock events in the PostgreSQL database."""
    if not events:
        return
    
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            # Use ON CONFLICT to handle duplicates
            insert_query = """
                INSERT INTO unlock_events 
                (token_symbol, contract_address, unlock_date, unlock_amount, 
                 circulating_supply, total_supply, source)
                VALUES (%(token_symbol)s, %(contract_address)s, %(unlock_date)s, 
                        %(unlock_amount)s, %(circulating_supply)s, %(total_supply)s, %(source)s)
                ON CONFLICT (contract_address, unlock_date) DO NOTHING
            """
            
            psycopg2.extras.execute_batch(cur, insert_query, events)
            conn.commit()
            logging.info(f"Successfully stored {len(events)} unlock events")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error storing unlock events: {e}")
        raise
    finally:
        conn.close()

def get_upcoming_unlocks(days_ahead: int) -> List[Dict[str, Any]]:
    """Retrieves unlock events scheduled within a given future timeframe."""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            end_date = datetime.now() + timedelta(days=days_ahead)
            
            cur.execute("""
                SELECT token_symbol, contract_address, unlock_date, unlock_amount,
                       circulating_supply, total_supply, source
                FROM unlock_events 
                WHERE unlock_date BETWEEN NOW() AND %s
                ORDER BY unlock_date ASC
            """, (end_date,))
            
            results = cur.fetchall()
            # Convert to list of dicts
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying upcoming unlocks: {e}")
        return []
    finally:
        conn.close()

def get_open_positions() -> List[Dict[str, Any]]:
    """Retrieves all open trading positions"""
    conn = _get_db_conn()
    try:
        with conn.cursor(cursor_factory=psycopg2.extras.RealDictCursor) as cur:
            cur.execute("""
                SELECT id as position_id, token_symbol, token_address, 
                       amount_shorted, entry_price_in_usdc, unlock_date, status
                FROM positions 
                WHERE status = 'OPEN'
                ORDER BY created_at ASC
            """)
            
            results = cur.fetchall()
            return [dict(row) for row in results]
    except Exception as e:
        logging.error(f"Error querying open positions: {e}")
        return []
    finally:
        conn.close()

def log_trade_entry(token_symbol: str, token_address: str, amount_shorted: float, 
                   entry_price: float, unlock_date: str, borrow_tx_hash: str = None, 
                   swap_tx_hash: str = None) -> int:
    """Log a new trade position to the database"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            cur.execute("""
                INSERT INTO positions 
                (token_symbol, token_address, amount_shorted, entry_price_in_usdc, 
                 unlock_date, borrow_tx_hash, swap_tx_hash)
                VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
            """, (token_symbol, token_address, amount_shorted, entry_price, 
                  unlock_date, borrow_tx_hash, swap_tx_hash))
            
            position_id = cur.fetchone()[0]
            conn.commit()
            logging.info(f"Logged new position with ID: {position_id}")
            return position_id
    except Exception as e:
        conn.rollback()
        logging.error(f"Error logging trade entry: {e}")
        raise
    finally:
        conn.close()

def update_position_status(position_id: int, status: str, close_tx_hash: str = None):
    """Update position status"""
    conn = _get_db_conn()
    try:
        with conn.cursor() as cur:
            if status == 'CLOSED':
                cur.execute("""
                    UPDATE positions 
                    SET status = %s, close_tx_hash = %s, closed_at = NOW()
                    WHERE id = %s
                """, (status, close_tx_hash, position_id))
            else:
                cur.execute("""
                    UPDATE positions 
                    SET status = %s
                    WHERE id = %s
                """, (status, position_id))
            
            conn.commit()
            logging.info(f"Updated position {position_id} status to {status}")
    except Exception as e:
        conn.rollback()
        logging.error(f"Error updating position status: {e}")
        raise
    finally:
        conn.close()
