"""
Configuration Management Module - 2025 Best Practices
====================================================

Centralized configuration management for Project Chimera with:
- Environment-based configuration
- Type validation and conversion
- Default values and validation
- Configuration hot-reloading
- Secrets management
- Environment-specific overrides
"""

import os
import logging
from typing import Any, Dict, Optional, Type, TypeVar, Union, List
from dataclasses import dataclass, field
from enum import Enum
import json


T = TypeVar('T')


class Environment(Enum):
    """Deployment environment types"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class DatabaseConfig:
    """Database configuration settings"""
    url: str = field(default_factory=lambda: os.getenv("DATABASE_URL", ""))
    pool_size: int = field(default_factory=lambda: int(os.getenv("DB_POOL_SIZE", "10")))
    max_overflow: int = field(default_factory=lambda: int(os.getenv("DB_MAX_OVERFLOW", "20")))
    pool_timeout: int = field(default_factory=lambda: int(os.getenv("DB_POOL_TIMEOUT", "30")))
    
    def __post_init__(self):
        if not self.url:
            raise ValueError("DATABASE_URL is required")


@dataclass
class RedisConfig:
    """Redis configuration settings"""
    url: str = field(default_factory=lambda: os.getenv("REDIS_URL", ""))
    max_connections: int = field(default_factory=lambda: int(os.getenv("REDIS_MAX_CONNECTIONS", "50")))
    retry_on_timeout: bool = field(default_factory=lambda: os.getenv("REDIS_RETRY_ON_TIMEOUT", "true").lower() == "true")
    
    def __post_init__(self):
        if not self.url:
            raise ValueError("REDIS_URL is required")


@dataclass
class TradingConfig:
    """Trading system configuration"""
    paper_trading_mode: bool = field(default_factory=lambda: os.getenv("PAPER_TRADING_MODE", "true").lower() == "true")
    borrow_amount_per_trade: float = field(default_factory=lambda: float(os.getenv("BORROW_AMOUNT_PER_TRADE", "1000")))
    stop_loss_pct: float = field(default_factory=lambda: float(os.getenv("STOP_LOSS_PCT", "0.15")))
    take_profit_pct: float = field(default_factory=lambda: float(os.getenv("TAKE_PROFIT_PCT", "0.10")))
    take_profit_days_before_unlock: int = field(default_factory=lambda: int(os.getenv("TAKE_PROFIT_DAYS_BEFORE_UNLOCK", "1")))
    pressure_score_threshold: float = field(default_factory=lambda: float(os.getenv("PRESSURE_SCORE_THRESHOLD", "0.75")))
    monitoring_interval_seconds: int = field(default_factory=lambda: int(os.getenv("MONITORING_INTERVAL_SECONDS", "60")))
    
    def __post_init__(self):
        # Validate trading parameters
        if not 0 < self.stop_loss_pct < 1:
            raise ValueError("STOP_LOSS_PCT must be between 0 and 1")
        if not 0 < self.take_profit_pct < 1:
            raise ValueError("TAKE_PROFIT_PCT must be between 0 and 1")
        if self.pressure_score_threshold < 0:
            raise ValueError("PRESSURE_SCORE_THRESHOLD must be positive")


@dataclass
class APIConfig:
    """External API configuration"""
    # Blockchain
    infura_api_key: str = field(default_factory=lambda: os.getenv("INFURA_API_KEY", ""))
    etherscan_api_key: str = field(default_factory=lambda: os.getenv("ETHERSCAN_API_KEY", ""))
    
    # Market Data
    coingecko_api_key: str = field(default_factory=lambda: os.getenv("COINGECKO_API_KEY", ""))
    dexscreener_api_key: str = field(default_factory=lambda: os.getenv("DEXSCREENER_API_KEY", ""))
    dextools_api_key: str = field(default_factory=lambda: os.getenv("DEXTOOLS_API_KEY", ""))
    
    # Analytics
    defillama_pro_api_key: str = field(default_factory=lambda: os.getenv("DEFILLAMA_PRO_API_KEY", ""))
    thegraph_api_key: str = field(default_factory=lambda: os.getenv("THEGRAPH_API_KEY", ""))
    
    # Notifications
    telegram_bot_token: str = field(default_factory=lambda: os.getenv("TELEGRAM_BOT_TOKEN", ""))
    telegram_chat_id: str = field(default_factory=lambda: os.getenv("TELEGRAM_CHAT_ID", ""))
    
    # Rate limiting
    api_rate_limit_per_second: int = field(default_factory=lambda: int(os.getenv("API_RATE_LIMIT_PER_SECOND", "10")))
    api_timeout_seconds: int = field(default_factory=lambda: int(os.getenv("API_TIMEOUT_SECONDS", "30")))
    
    def __post_init__(self):
        # Validate critical API keys
        if not self.infura_api_key:
            logging.warning("INFURA_API_KEY not set - blockchain operations may fail")


@dataclass
class SecurityConfig:
    """Security and secrets configuration"""
    private_key_path: str = field(default_factory=lambda: os.getenv("PRIVATE_KEY_PATH", "/etc/secrets/trader-pk"))
    enable_external_api_checks: bool = field(default_factory=lambda: os.getenv("ENABLE_EXTERNAL_API_CHECKS", "true").lower() == "true")
    max_position_size_usd: float = field(default_factory=lambda: float(os.getenv("MAX_POSITION_SIZE_USD", "10000")))
    daily_loss_limit_usd: float = field(default_factory=lambda: float(os.getenv("DAILY_LOSS_LIMIT_USD", "5000")))
    
    def __post_init__(self):
        if self.max_position_size_usd <= 0:
            raise ValueError("MAX_POSITION_SIZE_USD must be positive")
        if self.daily_loss_limit_usd <= 0:
            raise ValueError("DAILY_LOSS_LIMIT_USD must be positive")


@dataclass
class LoggingConfig:
    """Logging configuration"""
    level: str = field(default_factory=lambda: os.getenv("LOG_LEVEL", "INFO"))
    format: str = field(default_factory=lambda: os.getenv("LOG_FORMAT", "%(asctime)s - %(name)s - %(levelname)s - %(message)s"))
    file_path: Optional[str] = field(default_factory=lambda: os.getenv("LOG_FILE_PATH"))
    max_file_size_mb: int = field(default_factory=lambda: int(os.getenv("LOG_MAX_FILE_SIZE_MB", "100")))
    backup_count: int = field(default_factory=lambda: int(os.getenv("LOG_BACKUP_COUNT", "5")))
    
    def __post_init__(self):
        valid_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if self.level.upper() not in valid_levels:
            raise ValueError(f"LOG_LEVEL must be one of {valid_levels}")


@dataclass
class ChimeraConfig:
    """Main configuration class for Project Chimera"""
    environment: Environment = field(default_factory=lambda: Environment(os.getenv("ENVIRONMENT", "development")))
    debug: bool = field(default_factory=lambda: os.getenv("DEBUG", "false").lower() == "true")
    
    # Sub-configurations
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    redis: RedisConfig = field(default_factory=RedisConfig)
    trading: TradingConfig = field(default_factory=TradingConfig)
    api: APIConfig = field(default_factory=APIConfig)
    security: SecurityConfig = field(default_factory=SecurityConfig)
    logging: LoggingConfig = field(default_factory=LoggingConfig)
    
    def __post_init__(self):
        # Environment-specific validations
        if self.environment == Environment.PRODUCTION:
            if self.debug:
                logging.warning("Debug mode enabled in production environment")
            if self.trading.paper_trading_mode:
                logging.warning("Paper trading mode enabled in production environment")
    
    @classmethod
    def from_env(cls) -> 'ChimeraConfig':
        """Create configuration from environment variables"""
        return cls()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary (excluding sensitive data)"""
        config_dict = {}
        
        for field_name, field_value in self.__dict__.items():
            if hasattr(field_value, '__dict__'):
                # Handle nested dataclass
                nested_dict = {}
                for nested_name, nested_value in field_value.__dict__.items():
                    # Mask sensitive values
                    if any(sensitive in nested_name.lower() for sensitive in ['key', 'token', 'password', 'secret']):
                        nested_dict[nested_name] = "***MASKED***" if nested_value else None
                    else:
                        nested_dict[nested_name] = nested_value
                config_dict[field_name] = nested_dict
            else:
                config_dict[field_name] = field_value
        
        return config_dict
    
    def validate(self) -> List[str]:
        """Validate configuration and return list of issues"""
        issues = []
        
        # Check required configurations for production
        if self.environment == Environment.PRODUCTION:
            if not self.api.infura_api_key:
                issues.append("INFURA_API_KEY is required for production")
            if not self.api.telegram_bot_token:
                issues.append("TELEGRAM_BOT_TOKEN is required for production notifications")
            if self.trading.paper_trading_mode:
                issues.append("Paper trading mode should be disabled in production")
        
        # Check trading configuration consistency
        if self.trading.stop_loss_pct >= self.trading.take_profit_pct:
            issues.append("Stop loss percentage should be less than take profit percentage")
        
        # Check security limits
        if self.security.max_position_size_usd > 50000:
            issues.append("Maximum position size seems very high - consider reducing for safety")
        
        return issues


# Global configuration instance
config = ChimeraConfig.from_env()


def get_config() -> ChimeraConfig:
    """Get the global configuration instance"""
    return config


def reload_config() -> ChimeraConfig:
    """Reload configuration from environment variables"""
    global config
    config = ChimeraConfig.from_env()
    return config


def validate_config() -> bool:
    """Validate current configuration and log any issues"""
    issues = config.validate()
    
    if issues:
        logging.error("Configuration validation failed:")
        for issue in issues:
            logging.error(f"  - {issue}")
        return False
    
    logging.info("Configuration validation passed")
    return True


def log_config_summary():
    """Log a summary of the current configuration (excluding sensitive data)"""
    config_dict = config.to_dict()
    logging.info("Current configuration:")
    logging.info(f"  Environment: {config.environment.value}")
    logging.info(f"  Debug mode: {config.debug}")
    logging.info(f"  Paper trading: {config.trading.paper_trading_mode}")
    logging.info(f"  Stop loss: {config.trading.stop_loss_pct * 100:.1f}%")
    logging.info(f"  Take profit: {config.trading.take_profit_pct * 100:.1f}%")
    logging.info(f"  Pressure threshold: {config.trading.pressure_score_threshold}")


# Initialize logging based on configuration
def setup_logging():
    """Setup logging based on configuration"""
    log_config = config.logging
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_config.level.upper()),
        format=log_config.format
    )
    
    # Add file handler if specified
    if log_config.file_path:
        from logging.handlers import RotatingFileHandler
        
        file_handler = RotatingFileHandler(
            log_config.file_path,
            maxBytes=log_config.max_file_size_mb * 1024 * 1024,
            backupCount=log_config.backup_count
        )
        file_handler.setFormatter(logging.Formatter(log_config.format))
        logging.getLogger().addHandler(file_handler)


# Auto-setup logging when module is imported
setup_logging()
