# 🚀 Project Chimera - Complete Setup Guide

## 📋 **Required API Keys & Configuration**

### 🔑 **Critical APIs (REQUIRED for operation)**

#### 1. **Infura (Blockchain Access)**
- **What**: Ethereum node provider for blockchain interactions
- **Where to get**: [infura.io](https://infura.io)
- **Cost**: Free tier available (100k requests/day)
- **Setup**:
  1. Create account at infura.io
  2. Create new project
  3. Copy Project ID
- **Environment Variable**: `INFURA_API_KEY`
- **Current Status**: ⚠️ **HARDCODED IN .env.example - NEEDS REPLACEMENT**

#### 2. **TokenUnlocks.com API**
- **What**: Primary data source for token unlock events
- **Where to get**: [tokenunlocks.com/api](https://tokenunlocks.com/api)
- **Cost**: Paid service (~$50-100/month)
- **Environment Variable**: `TOKENUNLOCKS_API_KEY`
- **Current Status**: ❌ **MISSING**

#### 3. **Vestlab.io API**
- **What**: Secondary data source for vesting schedules
- **Where to get**: [vestlab.io](https://vestlab.io)
- **Cost**: Paid service
- **Environment Variable**: `VESTLAB_API_KEY`
- **Current Status**: ❌ **MISSING**

### 🔗 **Infrastructure (REQUIRED)**

#### 4. **PostgreSQL Database**
- **What**: Stores unlock events, positions, trade history
- **Where to get**: 
  - Local: Install PostgreSQL
  - Cloud: Render.com (free tier), AWS RDS, etc.
- **Environment Variable**: `DATABASE_URL`
- **Format**: `postgresql://user:password@host:port/database`
- **Current Status**: ❌ **NEEDS SETUP**

#### 5. **Redis**
- **What**: Pub/sub messaging between microservices
- **Where to get**:
  - Local: Install Redis
  - Cloud: Render.com (free tier), Redis Cloud, etc.
- **Environment Variable**: `REDIS_URL`
- **Format**: `redis://host:port` or `redis://user:password@host:port`
- **Current Status**: ❌ **NEEDS SETUP**

### 💰 **Trading Infrastructure (REQUIRED for live trading)**

#### 6. **Private Key & Wallet**
- **What**: Ethereum wallet for executing trades
- **Setup**: 
  1. Create new wallet (MetaMask, etc.)
  2. Fund with ETH for gas fees
  3. Store private key securely
- **Environment Variable**: `PRIVATE_KEY_PATH` (file path)
- **Security**: 🔒 **NEVER commit to git**
- **Current Status**: ❌ **NEEDS SETUP**

### 📱 **Optional APIs (for enhanced functionality)**

#### 7. **Telegram Bot (Notifications)**
- **What**: Real-time notifications and alerts
- **Where to get**: 
  1. Message @BotFather on Telegram
  2. Create new bot
  3. Get your chat ID
- **Environment Variables**: 
  - `TELEGRAM_BOT_TOKEN`
  - `TELEGRAM_CHAT_ID`
  - `TELEGRAM_API_ID`
  - `TELEGRAM_API_HASH`
- **Current Status**: ⚠️ **HARDCODED IN .env.example - NEEDS REPLACEMENT**

#### 8. **CoinGecko API (Price Data)**
- **What**: Token price and market data
- **Where to get**: [coingecko.com/api](https://coingecko.com/api)
- **Cost**: Free tier available, Pro plans for higher limits
- **Environment Variable**: `COINGECKO_API_KEY` (optional)
- **Current Status**: ✅ **OPTIONAL - Free tier works**

#### 9. **1inch API (DEX Aggregation)**
- **What**: Optimal swap routing for token trades
- **Where to get**: [1inch.io/api](https://1inch.io/api)
- **Cost**: Free tier available
- **Environment Variable**: `ONEINCH_API_KEY`
- **Current Status**: ❌ **MISSING**

## 🛠️ **Setup Priority**

### **Phase 1: Basic Testing (Local Development)**
```bash
# 1. Copy environment template
cp .env.example .env

# 2. Set up local infrastructure
# Install PostgreSQL and Redis locally
# OR use Docker:
docker run -d --name postgres -p 5432:5432 -e POSTGRES_PASSWORD=password postgres
docker run -d --name redis -p 6379:6379 redis

# 3. Update .env with local values
DATABASE_URL=postgresql://postgres:password@localhost:5432/chimera_db
REDIS_URL=redis://localhost:6379
```

### **Phase 2: API Integration**
1. **Get Infura API key** (free, immediate)
2. **Set up Telegram bot** (free, 5 minutes)
3. **Get CoinGecko API key** (free tier)
4. **Get 1inch API key** (free tier)

### **Phase 3: Data Sources (Paid)**
1. **TokenUnlocks.com API** ($50-100/month)
2. **Vestlab.io API** (pricing varies)

### **Phase 4: Live Trading**
1. **Create trading wallet**
2. **Fund with ETH for gas**
3. **Set up secure private key storage**

## 💸 **Cost Breakdown**

### **Free Tier (Testing)**
- Infura: Free (100k requests/day)
- CoinGecko: Free (50 calls/minute)
- 1inch: Free (limited)
- Telegram: Free
- Render.com: Free (750 hours/month)
- **Total: $0/month**

### **Production Ready**
- TokenUnlocks API: ~$75/month
- Vestlab API: ~$50/month
- Infura Pro: ~$50/month (if needed)
- **Total: ~$175/month**

## 🔒 **Security Checklist**

- [ ] Never commit API keys to git
- [ ] Use environment variables for all secrets
- [ ] Store private keys in secure files (not env vars)
- [ ] Use separate wallets for testing vs production
- [ ] Enable 2FA on all API accounts
- [ ] Monitor API usage and costs
- [ ] Set up alerts for unusual activity

## 🚀 **Quick Start Commands**

```bash
# 1. Clone and setup
git clone <your-repo>
cd project-chimera
cp .env.example .env

# 2. Install dependencies
pip install -r requirements.txt

# 3. Setup local infrastructure
python scripts/setup_local.py

# 4. Run tests
python -m pytest tests/ -v

# 5. Test individual services
python test_aave_integration.py
```

## 📞 **Support & Resources**

- **Infura**: [docs.infura.io](https://docs.infura.io)
- **TokenUnlocks**: [tokenunlocks.com/docs](https://tokenunlocks.com/docs)
- **Telegram Bots**: [core.telegram.org/bots](https://core.telegram.org/bots)
- **Render Deployment**: [render.com/docs](https://render.com/docs)
