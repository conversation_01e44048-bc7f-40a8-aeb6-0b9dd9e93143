import os
import time
import json
import redis
import logging
from decimal import Decimal

from db_handler import get_open_positions
from price_fetcher import get_realtime_price
from risk_manager import check_risk_rules

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# Redis setup
REDIS_URL = os.environ.get("REDIS_URL")

def get_redis_connection():
    """Get Redis connection"""
    if not REDIS_URL:
        raise Exception("REDIS_URL environment variable not set")
    return redis.from_url(REDIS_URL)

CLOSE_POSITION_CHANNEL = "chimera:close_position"

MONITORING_INTERVAL_SECONDS = int(os.environ.get("MONITORING_INTERVAL_SECONDS", "60"))  # Check prices every 60 seconds
PAPER_TRADING_MODE = os.environ.get("PAPER_TRADING_MODE", "true").lower() == "true"

def get_paper_trading_positions():
    """Get open positions from paper trading engine"""
    try:
        # Import here to avoid circular imports
        import sys
        from pathlib import Path
        executioner_path = Path(__file__).parent.parent / 'the-executioner'
        sys.path.insert(0, str(executioner_path))

        from paper_trading import paper_engine
        portfolio = paper_engine.get_portfolio_summary()
        return portfolio['open_positions']
    except Exception as e:
        logging.error(f"Error getting paper trading positions: {e}")
        return []

def monitor_positions():
    """Periodically fetches open positions and checks them against risk rules."""
    mode = "PAPER TRADING" if PAPER_TRADING_MODE else "LIVE TRADING"
    logging.info(f"The Ledger is starting its monitoring loop in {mode} mode.")

    while True:
        try:
            # 1. Get all open positions (from DB or paper trading engine)
            if PAPER_TRADING_MODE:
                open_positions = get_paper_trading_positions()
                logging.info("📋 Monitoring paper trading positions")
            else:
                open_positions = get_open_positions()
                logging.info("📋 Monitoring live trading positions")

            if not open_positions:
                logging.info("No open positions to monitor.")
            else:
                logging.info(f"Monitoring {len(open_positions)} open position(s)...")

            for pos in open_positions:
                try:
                    # 2. Get the current market price
                    current_price = get_realtime_price(pos['token_address'])
                    if not current_price:
                        logging.error(f"Could not fetch price for {pos['token_symbol']}. Skipping check.")
                        continue

                    logging.info(f"Current price for {pos['token_symbol']}: ${current_price:.4f}")

                    # 3. Check against risk rules (stop-loss, take-profit)
                    action, reason = check_risk_rules(pos, current_price)

                    # 4. If a rule is triggered, publish a CLOSE_POSITION event
                    if action == "CLOSE":
                        logging.warning(f"RISK TRIGGER: {reason} for {pos['token_symbol']}. Publishing close event.")
                        close_event = {
                            "position_id": pos['position_id'],
                            "token_symbol": pos['token_symbol'],
                            "token_address": pos['token_address'],
                            "current_price": str(current_price),
                            "entry_price": str(pos['entry_price_in_usdc']),
                            "reason": reason,
                            "paper_trade": PAPER_TRADING_MODE
                        }

                        r = get_redis_connection()
                        r.publish(CLOSE_POSITION_CHANNEL, json.dumps(close_event, default=str))

                        # Handle position closing based on mode
                        if PAPER_TRADING_MODE:
                            # Close paper trading position
                            try:
                                import sys
                                from pathlib import Path
                                executioner_path = Path(__file__).parent.parent / 'the-executioner'
                                sys.path.insert(0, str(executioner_path))

                                from paper_trading import paper_engine
                                closed_position = paper_engine.simulate_position_close(
                                    pos['position_id'], reason, current_price
                                )
                                logging.warning(f"📋 Paper position {pos['position_id']} closed")
                            except Exception as e:
                                logging.error(f"Error closing paper position: {e}")
                        else:
                            # Update position status in DB to 'CLOSING' to prevent re-triggering
                            from db_handler import update_position_status
                            update_position_status(pos['position_id'], 'CLOSING')

                        logging.warning(f"Position {pos['position_id']} marked for closing")
                    else:
                        logging.info(f"Position {pos['position_id']} ({pos['token_symbol']}): {action}")
                        
                except Exception as e:
                    logging.error(f"Error processing position {pos.get('position_id', 'unknown')}: {e}")
                    continue
                    
        except Exception as e:
            logging.error(f"An error occurred in the monitoring loop: {e}")

        # Sleep before next check
        logging.info(f"Sleeping for {MONITORING_INTERVAL_SECONDS} seconds...")
        time.sleep(MONITORING_INTERVAL_SECONDS)

def run_single_check():
    """Run a single check of all positions (useful for testing)"""
    logging.info("Running single position check...")
    
    try:
        open_positions = get_open_positions()
        
        if not open_positions:
            logging.info("No open positions to check.")
            return
            
        logging.info(f"Checking {len(open_positions)} position(s)...")
        
        for pos in open_positions:
            try:
                current_price = get_realtime_price(pos['token_address'])
                if not current_price:
                    logging.error(f"Could not fetch price for {pos['token_symbol']}")
                    continue
                
                action, reason = check_risk_rules(pos, current_price)
                
                logging.info(f"Position {pos['position_id']} ({pos['token_symbol']}): {action}")
                if reason:
                    logging.info(f"  Reason: {reason}")
                    
            except Exception as e:
                logging.error(f"Error checking position {pos.get('position_id', 'unknown')}: {e}")
                
    except Exception as e:
        logging.error(f"Error in single check: {e}")

if __name__ == "__main__":
    # Check if we should run in single-check mode (useful for testing)
    if os.environ.get("SINGLE_CHECK", "false").lower() == "true":
        run_single_check()
    else:
        monitor_positions()
