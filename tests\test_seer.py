#!/usr/bin/env python3
"""
Tests for The Seer service
"""

import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# Add the seer service to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'services', 'the-seer'))

from analysis import calculate_unlock_pressure_score, calculate_risk_metrics
from onchain_checker import is_token_borrowable, is_major_token

class TestAnalysis(unittest.TestCase):
    
    def test_calculate_unlock_pressure_score(self):
        """Test pressure score calculation"""
        test_event = {
            'token_symbol': 'TEST',
            'unlock_amount': 100000000,
            'circulating_supply': 500000000,
            'volume_24h': 50000000
        }
        
        score = calculate_unlock_pressure_score(test_event)
        
        # Score should be a float
        self.assertIsInstance(score, float)
        
        # Score should be positive for valid data
        self.assertGreaterEqual(score, 0)
        
        # Test calculation: (100M / 500M) * (100M / 50M) = 0.2 * 2 = 0.4
        expected_score = (100000000 / 500000000) * (100000000 / 50000000)
        self.assertAlmostEqual(score, expected_score, places=6)
    
    def test_calculate_pressure_score_zero_values(self):
        """Test pressure score with zero values"""
        test_event = {
            'token_symbol': 'TEST',
            'unlock_amount': 100000000,
            'circulating_supply': 0,  # Zero supply
            'volume_24h': 50000000
        }
        
        score = calculate_unlock_pressure_score(test_event)
        self.assertEqual(score, 0.0)
    
    def test_calculate_risk_metrics(self):
        """Test risk metrics calculation"""
        test_event = {
            'unlock_amount': 100000000,
            'circulating_supply': 500000000,
            'total_supply': 1000000000
        }
        
        metrics = calculate_risk_metrics(test_event)
        
        # Check required fields
        self.assertIn('unlock_percentage', metrics)
        self.assertIn('circulation_ratio', metrics)
        self.assertIn('dilution_impact', metrics)
        
        # Check calculations
        expected_unlock_pct = (100000000 / 500000000) * 100  # 20%
        self.assertAlmostEqual(metrics['unlock_percentage'], expected_unlock_pct, places=2)

class TestOnchainChecker(unittest.TestCase):
    
    def test_is_major_token(self):
        """Test major token detection"""
        # Test known major tokens
        uni_address = "0x1f9840a85d5af5bf1d1762f925bdaddc4201f984"
        self.assertTrue(is_major_token(uni_address))
        
        # Test case insensitive
        self.assertTrue(is_major_token(uni_address.upper()))
        
        # Test unknown token
        unknown_address = "0x1234567890123456789012345678901234567890"
        self.assertFalse(is_major_token(unknown_address))
    
    @patch('onchain_checker.requests.post')
    def test_is_borrowable_on_aave_success(self, mock_post):
        """Test Aave borrowability check - success case"""
        # Mock successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "data": {
                "reserve": {
                    "id": "test",
                    "borrowingEnabled": True,
                    "isActive": True,
                    "isFrozen": False
                }
            }
        }
        mock_post.return_value = mock_response
        
        from onchain_checker import is_borrowable_on_aave
        result = is_borrowable_on_aave("0x1234567890123456789012345678901234567890")
        self.assertTrue(result)
    
    @patch('onchain_checker.requests.post')
    def test_is_borrowable_on_aave_not_borrowable(self, mock_post):
        """Test Aave borrowability check - not borrowable"""
        # Mock response with borrowing disabled
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {
            "data": {
                "reserve": {
                    "id": "test",
                    "borrowingEnabled": False,
                    "isActive": True,
                    "isFrozen": False
                }
            }
        }
        mock_post.return_value = mock_response
        
        from onchain_checker import is_borrowable_on_aave
        result = is_borrowable_on_aave("0x1234567890123456789012345678901234567890")
        self.assertFalse(result)

class TestSeerIntegration(unittest.TestCase):
    
    @patch('onchain_checker.is_token_borrowable')
    def test_high_score_borrowable_token(self, mock_borrowable):
        """Test that high score + borrowable token should be flagged"""
        mock_borrowable.return_value = True
        
        # High impact unlock event
        test_event = {
            'token_symbol': 'HIGHIMPACT',
            'contract_address': '0x1234567890123456789012345678901234567890',
            'unlock_amount': 200000000,  # Large unlock
            'circulating_supply': 100000000,  # Small supply
            'volume_24h': 10000000  # Low volume
        }
        
        score = calculate_unlock_pressure_score(test_event)
        
        # This should result in a high score
        # (200M / 100M) * (200M / 10M) = 2 * 20 = 40
        self.assertGreater(score, 1.0)  # Well above typical threshold
    
    @patch('onchain_checker.is_token_borrowable')
    def test_low_score_should_be_filtered(self, mock_borrowable):
        """Test that low score events are filtered out"""
        mock_borrowable.return_value = True
        
        # Low impact unlock event
        test_event = {
            'token_symbol': 'LOWIMPACT',
            'contract_address': '0x1234567890123456789012345678901234567890',
            'unlock_amount': 1000000,  # Small unlock
            'circulating_supply': 1000000000,  # Large supply
            'volume_24h': 100000000  # High volume
        }
        
        score = calculate_unlock_pressure_score(test_event)
        
        # This should result in a low score
        # (1M / 1000M) * (1M / 100M) = 0.001 * 0.01 = 0.00001
        self.assertLess(score, 0.1)  # Well below typical threshold

if __name__ == '__main__':
    unittest.main()
