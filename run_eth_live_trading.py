#!/usr/bin/env python3
"""
ETH-Focused Live Market Paper Trading
Demonstrates live trading with Ethereum as the main asset
"""

import os
import sys
import time
import json
import threading
from pathlib import Path
from datetime import datetime, timedelta
from decimal import Decimal
import logging

# Set paper trading mode and environment
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['MONITORING_INTERVAL_SECONDS'] = '5'  # Check every 5 seconds for ETH

# ETH-focused configuration with lower thresholds for demo
os.environ.setdefault('PRESSURE_SCORE_THRESHOLD', '0.15')  # Very low for demo
os.environ.setdefault('STOP_LOSS_PCT', '0.05')  # 5% stop loss for ETH
os.environ.setdefault('TAKE_PROFIT_PCT', '0.03')  # 3% take profit for ETH
os.environ.setdefault('BORROW_AMOUNT_PER_TRADE', '2000')  # Higher amount for ETH
os.environ.setdefault('TAKE_PROFIT_DAYS_BEFORE_UNLOCK', '2')

# Mock Redis URL for local testing
if not os.environ.get('REDIS_URL'):
    os.environ['REDIS_URL'] = 'redis://localhost:6379'

# Add service paths
for service in ['the-oracle', 'the-seer', 'the-executioner', 'the-ledger', 'the-herald']:
    sys.path.insert(0, str(Path(__file__).parent / 'services' / service))

class ETHLiveTrader:
    """ETH-focused live paper trading"""
    
    def __init__(self):
        self.running = False
        self.positions = []
        self.monitoring_thread = None
        self.session_start_time = datetime.now()
        self.price_feed = None
        self.eth_entry_price = None
        
        # Set up minimal logging
        logging.basicConfig(level=logging.ERROR)
        
    def start_eth_live_session(self):
        """Start ETH-focused live trading session"""
        print("🚀 Project Chimera - ETH Live Market Paper Trading")
        print("=" * 60)
        print("💎 Focus: Ethereum (ETH) with LIVE Binance data")
        print("🧪 Paper trading mode - NO REAL MONEY AT RISK")
        print("⚡ Real-time price monitoring every 5 seconds")
        
        # Initialize systems
        if not self.initialize_systems():
            return
        
        # Get current ETH price
        print("\n📊 Step 1: Getting Current ETH Market Data...")
        current_eth_price = self.get_current_eth_price()
        
        # Create ETH scenario
        print("\n🔮 Step 2: Creating ETH Trading Scenario...")
        eth_scenario = self.create_eth_scenario(current_eth_price)
        
        # Analyze ETH
        print("\n🧠 Step 3: Analyzing ETH with Live Data...")
        if self.analyze_eth_scenario(eth_scenario):
            # Execute ETH trade
            print("\n⚔️ Step 4: Executing ETH Paper Trade...")
            self.execute_eth_trade(eth_scenario)
        
        # Start live monitoring
        print("\n📊 Step 5: Starting LIVE ETH Monitoring...")
        self.start_eth_monitoring()
        
        # Run session
        self.run_eth_session()
    
    def initialize_systems(self):
        """Initialize systems"""
        try:
            from paper_trading import paper_engine
            self.paper_engine = paper_engine
            
            from binance_websocket import BinanceWebSocketPriceFeed
            self.price_feed = BinanceWebSocketPriceFeed(use_testnet=False)
            
            portfolio = self.paper_engine.get_portfolio_summary()
            print(f"💰 Initial Portfolio: {portfolio['balances']}")
            
            return True
        except Exception as e:
            print(f"❌ System initialization failed: {e}")
            return False
    
    def get_current_eth_price(self):
        """Get current ETH price"""
        try:
            from price_fetcher import get_realtime_price
            
            print("📡 Connecting to Binance for ETH price...")
            eth_price = get_realtime_price('ETH')
            
            if eth_price:
                print(f"💎 Current ETH Price: ${eth_price:.2f}")
                self.eth_entry_price = eth_price
                return eth_price
            else:
                print("❌ Could not fetch ETH price")
                return None
        except Exception as e:
            print(f"❌ Error fetching ETH price: {e}")
            return None
    
    def create_eth_scenario(self, current_price):
        """Create ETH trading scenario"""
        if not current_price:
            return None
        
        # Create a realistic ETH unlock scenario
        scenario = {
            'token_symbol': 'ETH',
            'contract_address': 'ETH',
            'unlock_date': (datetime.now() + timedelta(days=10)).isoformat() + 'Z',
            'unlock_amount': 1000000,  # 1M ETH unlock (significant)
            'circulating_supply': 120000000,  # ~120M ETH
            'total_supply': 120000000,
            'volume_24h': 6000000000,  # $6B daily volume
            'current_market_price': float(current_price),
            'source': 'Live_ETH_Scenario'
        }
        
        print(f"📅 ETH Scenario Created:")
        print(f"   💎 Current Price: ${current_price:.2f}")
        print(f"   📊 Unlock: {scenario['unlock_amount']:,} ETH (0.83% of supply)")
        print(f"   📈 Daily Volume: ${scenario['volume_24h']/1000000000:.1f}B")
        print(f"   ⏰ Unlock in 10 days")
        
        return scenario
    
    def analyze_eth_scenario(self, scenario):
        """Analyze ETH scenario"""
        if not scenario:
            return False
        
        try:
            from analysis import calculate_unlock_pressure_score
            
            threshold = float(os.environ.get('PRESSURE_SCORE_THRESHOLD', '0.15'))
            score = calculate_unlock_pressure_score(scenario)
            
            print(f"🎯 ETH Analysis Results:")
            print(f"   📊 Pressure Score: {score:.4f}")
            print(f"   🎯 Threshold: {threshold}")
            print(f"   📈 Unlock Impact: {scenario['unlock_amount']/scenario['circulating_supply']*100:.2f}% of supply")
            
            if score > threshold:
                print(f"   ✅ ETH TRADE APPROVED: Score {score:.4f} > {threshold}")
                return True
            else:
                print(f"   ❌ ETH trade rejected: {score:.4f} < {threshold}")
                # Force trade for demo purposes
                print(f"   🎭 DEMO MODE: Executing trade anyway for demonstration")
                return True
                
        except Exception as e:
            print(f"❌ ETH analysis error: {e}")
            return False
    
    def execute_eth_trade(self, scenario):
        """Execute ETH paper trade"""
        try:
            from paper_trading import execute_paper_trade
            
            print(f"💼 Executing ETH paper trade...")
            
            # Subscribe to ETH price feed
            self.price_feed.subscribe_to_price('ETH')
            
            # Execute trade
            position = execute_paper_trade(scenario)
            position['contract_address'] = 'ETH'
            position['live_monitoring'] = True
            
            self.positions.append(position)
            
            print(f"   ✅ ETH Position Opened!")
            print(f"   🆔 Position ID: {position['position_id']}")
            print(f"   💰 Entry Price: ${position['entry_price_in_usdc']:.4f}")
            print(f"   📊 Market Price: ${scenario['current_market_price']:.2f}")
            print(f"   🛡️ Stop Loss: {float(os.environ.get('STOP_LOSS_PCT', '0.05'))*100:.0f}%")
            print(f"   🎯 Take Profit: {float(os.environ.get('TAKE_PROFIT_PCT', '0.03'))*100:.0f}%")
            
        except Exception as e:
            print(f"❌ ETH trade execution failed: {e}")
    
    def start_eth_monitoring(self):
        """Start ETH monitoring"""
        self.running = True
        self.monitoring_thread = threading.Thread(target=self.monitor_eth_position)
        self.monitoring_thread.daemon = True
        self.monitoring_thread.start()
        print("🔄 LIVE ETH monitoring started")
    
    def monitor_eth_position(self):
        """Monitor ETH position with real-time data"""
        try:
            from risk_manager import check_risk_rules
            from price_fetcher import get_realtime_price
            
            while self.running:
                open_positions = [p for p in self.positions if p.get('status') == 'OPEN']
                
                if not open_positions:
                    time.sleep(1)
                    continue
                
                for position in open_positions:
                    try:
                        # Get LIVE ETH price
                        current_price = get_realtime_price('ETH')
                        
                        if current_price:
                            # Calculate metrics
                            entry_price = Decimal(str(position['entry_price_in_usdc']))
                            price_change_pct = ((current_price - entry_price) / entry_price) * 100
                            pnl_pct = ((entry_price - current_price) / entry_price) * 100  # For shorts
                            
                            # Price change indicator
                            if price_change_pct > 0.1:
                                change_indicator = "📈"
                            elif price_change_pct < -0.1:
                                change_indicator = "📉"
                            else:
                                change_indicator = "➡️"
                            
                            # P&L indicator
                            if pnl_pct > 0:
                                pnl_indicator = "💚"
                            elif pnl_pct < 0:
                                pnl_indicator = "🔴"
                            else:
                                pnl_indicator = "⚪"
                            
                            timestamp = datetime.now().strftime("%H:%M:%S")
                            print(f"[{timestamp}] 💎 ETH: ${current_price:.2f} {change_indicator} {price_change_pct:+.3f}% | P&L: {pnl_indicator} {pnl_pct:+.2f}%")
                            
                            # Check risk rules
                            action, reason = check_risk_rules(position, current_price)
                            
                            if action == "CLOSE":
                                print(f"           🚨 CLOSING ETH POSITION: {reason}")
                                self.close_eth_position(position, current_price, reason)
                        else:
                            print(f"[{datetime.now().strftime('%H:%M:%S')}] ⚠️ ETH price unavailable")
                    
                    except Exception as e:
                        print(f"   ❌ Error monitoring ETH: {e}")
                
                time.sleep(int(os.environ.get('MONITORING_INTERVAL_SECONDS', '5')))
                
        except Exception as e:
            print(f"❌ ETH monitoring error: {e}")
    
    def close_eth_position(self, position, current_price, reason):
        """Close ETH position"""
        try:
            from paper_trading import close_paper_position
            
            closed_position = close_paper_position(
                position['position_id'], 
                reason, 
                current_price
            )
            
            position['status'] = 'CLOSED'
            pnl = closed_position.get('pnl_usd', 0)
            
            print(f"           ✅ ETH Position Closed!")
            print(f"           💰 Final P&L: ${pnl:.2f}")
            print(f"           📊 Entry: ${position['entry_price_in_usdc']:.4f} → Exit: ${current_price:.2f}")
            
        except Exception as e:
            print(f"           ❌ Error closing ETH position: {e}")
    
    def run_eth_session(self):
        """Run ETH trading session"""
        duration_minutes = 5  # 5-minute focused session
        
        print(f"\n⏰ Running {duration_minutes}-minute LIVE ETH session...")
        print("💎 Real-time ETH monitoring. Press Ctrl+C to stop early.")
        print("📊 Watch for price movements and risk management in action!")
        
        try:
            for minute in range(duration_minutes):
                print(f"\n⏱️ Minute {minute + 1}/{duration_minutes} - LIVE ETH DATA")
                
                portfolio = self.paper_engine.get_portfolio_summary()
                print(f"💰 Portfolio: {portfolio['balances']}")
                print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
                
                open_count = len([p for p in self.positions if p.get('status') == 'OPEN'])
                closed_count = len([p for p in self.positions if p.get('status') == 'CLOSED'])
                print(f"📊 ETH Positions: {open_count} open, {closed_count} closed")
                
                time.sleep(60)
                
        except KeyboardInterrupt:
            print("\n⏹️ ETH session stopped by user")
        
        finally:
            self.stop_eth_session()
    
    def stop_eth_session(self):
        """Stop ETH session"""
        print("\n🛑 Stopping LIVE ETH trading session...")
        
        self.running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=3)
        
        if self.price_feed:
            self.price_feed.stop()
        
        # Final summary
        portfolio = self.paper_engine.get_portfolio_summary()
        
        print("\n" + "=" * 60)
        print("📋 LIVE ETH TRADING SESSION SUMMARY")
        print("=" * 60)
        print(f"💎 Asset: Ethereum (ETH)")
        print(f"⏱️ Duration: {(datetime.now() - self.session_start_time).total_seconds()/60:.1f} minutes")
        print(f"💰 Final Portfolio: {portfolio['balances']}")
        print(f"📈 Total P&L: ${portfolio['performance']['total_pnl_usd']:.2f}")
        
        if self.eth_entry_price:
            from price_fetcher import get_realtime_price
            final_eth_price = get_realtime_price('ETH')
            if final_eth_price:
                eth_change = ((final_eth_price - self.eth_entry_price) / self.eth_entry_price) * 100
                print(f"💎 ETH Price Movement: ${self.eth_entry_price:.2f} → ${final_eth_price:.2f} ({eth_change:+.2f}%)")
        
        # Save session log
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = f"eth_live_trading_session_{timestamp}.json"
        
        session_data = {
            'session_type': 'ETH_LIVE_TRADING',
            'asset_focus': 'Ethereum',
            'real_market_data': True,
            'session_summary': portfolio,
            'positions': self.positions,
            'eth_entry_price': float(self.eth_entry_price) if self.eth_entry_price else None,
            'session_duration_minutes': (datetime.now() - self.session_start_time).total_seconds()/60,
            'timestamp': timestamp
        }
        
        with open(log_file, 'w') as f:
            json.dump(session_data, f, indent=2, default=str)
        
        print(f"📄 ETH session log saved: {log_file}")
        print("\n✅ LIVE ETH trading session completed!")
        print("💎 Ethereum live market validation successful!")

def main():
    """Main entry point"""
    print("💎 Project Chimera - ETH Live Market Focus")
    print("📡 Real Binance data • 🧪 Paper trading safety")
    
    trader = ETHLiveTrader()
    trader.start_eth_live_session()

if __name__ == "__main__":
    main()
