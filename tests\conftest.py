"""
Pytest Configuration and Fixtures - 2025 Best Practices
======================================================

This module provides shared pytest fixtures and configuration for the Project Chimera test suite.
It implements modern testing patterns including:
- Async testing support
- Database fixtures with cleanup
- Mock API responses
- Test data factories
- Performance testing utilities
"""

import pytest
import asyncio
import os
import sys
import logging
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, Any, List, Generator
from datetime import datetime, timezone, timedelta
import tempfile
import json

# Add services to path for testing
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'services'))

# Configure test logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture(scope="session")
def test_config():
    """Test configuration with safe defaults"""
    return {
        'ENVIRONMENT': 'testing',
        'DEBUG': 'true',
        'PAPER_TRADING_MODE': 'true',
        'ENABLE_EXTERNAL_API_CHECKS': 'false',
        'STOP_LOSS_PCT': '0.15',
        'TAKE_PROFIT_PCT': '0.10',
        'PRESSURE_SCORE_THRESHOLD': '0.75',
        'BORROW_AMOUNT_PER_TRADE': '1000',
        'MONITORING_INTERVAL_SECONDS': '60',
        'API_RATE_LIMIT_PER_SECOND': '100',  # Higher for testing
        'API_TIMEOUT_SECONDS': '10',  # Lower for testing
        'LOG_LEVEL': 'DEBUG'
    }


@pytest.fixture(autouse=True)
def setup_test_environment(test_config):
    """Automatically setup test environment variables for all tests"""
    original_env = {}
    
    # Store original values and set test values
    for key, value in test_config.items():
        original_env[key] = os.environ.get(key)
        os.environ[key] = value
    
    yield
    
    # Restore original values
    for key, original_value in original_env.items():
        if original_value is None:
            os.environ.pop(key, None)
        else:
            os.environ[key] = original_value


@pytest.fixture
def mock_redis():
    """Mock Redis client for testing"""
    mock_redis = MagicMock()
    mock_pubsub = MagicMock()
    mock_redis.pubsub.return_value = mock_pubsub
    mock_redis.publish.return_value = True
    mock_pubsub.listen.return_value = []
    return mock_redis


@pytest.fixture
def mock_database():
    """Mock database connection for testing"""
    mock_db = MagicMock()
    mock_cursor = MagicMock()
    mock_db.cursor.return_value.__enter__.return_value = mock_cursor
    mock_cursor.fetchall.return_value = []
    mock_cursor.fetchone.return_value = None
    return mock_db


@pytest.fixture
def sample_unlock_event():
    """Sample unlock event data for testing"""
    return {
        'token_symbol': 'TEST',
        'contract_address': '0x1234567890123456789012345678901234567890',
        'unlock_date': (datetime.now(timezone.utc) + timedelta(days=7)).isoformat(),
        'unlock_amount': 1000000.0,
        'circulating_supply': 10000000.0,
        'total_supply': 100000000.0,
        'source': 'test_data',
        'confidence': 'high',
        'created_at': datetime.now(timezone.utc).isoformat()
    }


@pytest.fixture
def sample_position():
    """Sample trading position for testing"""
    return {
        'position_id': 1,
        'token_symbol': 'TEST',
        'token_address': '0x1234567890123456789012345678901234567890',
        'amount_shorted': 1000.0,
        'entry_price_in_usdc': 2.0,
        'unlock_date': (datetime.now(timezone.utc) + timedelta(days=7)).isoformat(),
        'status': 'OPEN',
        'borrow_tx_hash': '0x' + '0' * 64,
        'swap_tx_hash': '0x' + '1' * 64,
        'created_at': datetime.now(timezone.utc).isoformat()
    }


@pytest.fixture
def mock_api_responses():
    """Mock API responses for external services"""
    return {
        'coingecko_markets': [
            {
                'id': 'test-token',
                'symbol': 'test',
                'name': 'Test Token',
                'current_price': 2.0,
                'price_change_percentage_24h': -15.5,
                'price_change_percentage_7d': -25.2,
                'market_cap': 20000000,
                'total_volume': 1000000
            }
        ],
        'defillama_protocols': [
            {
                'name': 'Test Protocol',
                'symbol': 'TEST',
                'tvl': 50000000,
                'change_1d': -20.5,
                'change_7d': -30.2,
                'category': 'DEX'
            }
        ],
        'aave_reserve': {
            'data': {
                'reserve': {
                    'id': 'test-reserve',
                    'borrowingEnabled': True,
                    'isActive': True,
                    'isFrozen': False,
                    'symbol': 'TEST'
                }
            }
        }
    }


@pytest.fixture
def mock_web3():
    """Mock Web3 instance for blockchain testing"""
    mock_web3 = MagicMock()
    mock_web3.is_connected.return_value = True
    mock_web3.eth.block_number = ********
    mock_web3.eth.gas_price = ***********  # 20 gwei
    
    # Mock account
    mock_account = MagicMock()
    mock_account.address = '0x' + 'a' * 40
    
    # Mock contract
    mock_contract = MagicMock()
    mock_web3.eth.contract.return_value = mock_contract
    
    return mock_web3, mock_account


@pytest.fixture
def temp_file():
    """Create a temporary file for testing"""
    with tempfile.NamedTemporaryFile(mode='w+', delete=False) as f:
        yield f.name
    os.unlink(f.name)


@pytest.fixture
def mock_requests():
    """Mock requests module for API testing"""
    with patch('requests.get') as mock_get, \
         patch('requests.post') as mock_post:
        
        # Default successful response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.raise_for_status.return_value = None
        mock_response.json.return_value = {'status': 'success'}
        
        mock_get.return_value = mock_response
        mock_post.return_value = mock_response
        
        yield {
            'get': mock_get,
            'post': mock_post,
            'response': mock_response
        }


class TestDataFactory:
    """Factory for creating test data"""
    
    @staticmethod
    def create_unlock_events(count: int = 3) -> List[Dict[str, Any]]:
        """Create multiple unlock events for testing"""
        events = []
        base_date = datetime.now(timezone.utc)
        
        for i in range(count):
            events.append({
                'token_symbol': f'TOKEN{i}',
                'contract_address': f'0x{"1" * 39}{i}',
                'unlock_date': (base_date + timedelta(days=i+1)).isoformat(),
                'unlock_amount': 1000000.0 * (i + 1),
                'circulating_supply': 10000000.0 * (i + 1),
                'total_supply': 100000000.0 * (i + 1),
                'source': 'test_factory',
                'confidence': 'medium'
            })
        
        return events
    
    @staticmethod
    def create_positions(count: int = 2) -> List[Dict[str, Any]]:
        """Create multiple positions for testing"""
        positions = []
        base_date = datetime.now(timezone.utc)
        
        for i in range(count):
            positions.append({
                'position_id': i + 1,
                'token_symbol': f'TOKEN{i}',
                'token_address': f'0x{"2" * 39}{i}',
                'amount_shorted': 1000.0 * (i + 1),
                'entry_price_in_usdc': 2.0 + i * 0.5,
                'unlock_date': (base_date + timedelta(days=i+7)).isoformat(),
                'status': 'OPEN',
                'borrow_tx_hash': f'0x{"a" * 63}{i}',
                'swap_tx_hash': f'0x{"b" * 63}{i}',
                'created_at': base_date.isoformat()
            })
        
        return positions


@pytest.fixture
def test_data_factory():
    """Provide test data factory"""
    return TestDataFactory


@pytest.fixture
def performance_timer():
    """Timer for performance testing"""
    import time
    
    class Timer:
        def __init__(self):
            self.start_time = None
            self.end_time = None
        
        def start(self):
            self.start_time = time.time()
        
        def stop(self):
            self.end_time = time.time()
            return self.elapsed
        
        @property
        def elapsed(self):
            if self.start_time and self.end_time:
                return self.end_time - self.start_time
            return None
    
    return Timer()


@pytest.fixture
def capture_logs():
    """Capture log messages for testing"""
    import logging
    from io import StringIO
    
    log_capture = StringIO()
    handler = logging.StreamHandler(log_capture)
    handler.setLevel(logging.DEBUG)
    
    # Add handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(handler)
    
    yield log_capture
    
    # Remove handler
    root_logger.removeHandler(handler)


# Pytest configuration
def pytest_configure(config):
    """Configure pytest with custom markers"""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "unit: marks tests as unit tests"
    )
    config.addinivalue_line(
        "markers", "api: marks tests that require external API access"
    )


def pytest_collection_modifyitems(config, items):
    """Modify test collection to add markers automatically"""
    for item in items:
        # Mark slow tests
        if "slow" in item.nodeid or any(keyword in item.name.lower() 
                                       for keyword in ["integration", "end_to_end"]):
            item.add_marker(pytest.mark.slow)
        
        # Mark integration tests
        if "integration" in item.nodeid or "test_integration" in item.nodeid:
            item.add_marker(pytest.mark.integration)
        
        # Mark unit tests
        if not any(marker in item.nodeid for marker in ["integration", "slow"]):
            item.add_marker(pytest.mark.unit)


# Custom assertions
def assert_valid_unlock_event(event: Dict[str, Any]):
    """Assert that an event has valid unlock event structure"""
    required_fields = ['token_symbol', 'contract_address', 'unlock_date', 'unlock_amount', 'source']
    
    for field in required_fields:
        assert field in event, f"Missing required field: {field}"
    
    assert isinstance(event['token_symbol'], str), "token_symbol must be string"
    assert isinstance(event['contract_address'], str), "contract_address must be string"
    assert isinstance(event['unlock_date'], str), "unlock_date must be string"
    assert isinstance(event['unlock_amount'], (int, float)), "unlock_amount must be numeric"
    assert event['contract_address'].startswith('0x'), "contract_address must start with 0x"
    assert len(event['contract_address']) >= 42, "contract_address must be at least 42 characters"


def assert_valid_position(position: Dict[str, Any]):
    """Assert that a position has valid structure"""
    required_fields = ['position_id', 'token_symbol', 'token_address', 'amount_shorted', 
                      'entry_price_in_usdc', 'status']
    
    for field in required_fields:
        assert field in position, f"Missing required field: {field}"
    
    assert isinstance(position['position_id'], int), "position_id must be integer"
    assert isinstance(position['amount_shorted'], (int, float)), "amount_shorted must be numeric"
    assert isinstance(position['entry_price_in_usdc'], (int, float)), "entry_price_in_usdc must be numeric"
    assert position['status'] in ['OPEN', 'CLOSING', 'CLOSED'], "Invalid position status"
