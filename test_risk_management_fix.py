#!/usr/bin/env python3
"""
Focused test script for the Risk Management Logic Fix
Tests the critical stop-loss priority issue identified in the paper trading results
"""

import os
import sys
from pathlib import Path
from decimal import Decimal
from datetime import datetime, timedelta

# Set test environment
os.environ['PAPER_TRADING_MODE'] = 'true'
os.environ['STOP_LOSS_PCT'] = '0.15'  # 15% stop loss
os.environ['TAKE_PROFIT_PCT'] = '0.10'  # 10% take profit
os.environ['TAKE_PROFIT_DAYS_BEFORE_UNLOCK'] = '1'  # 1 day before unlock

# Add service paths
sys.path.insert(0, str(Path(__file__).parent / 'services' / 'the-ledger'))

def test_stop_loss_priority():
    """
    Test that stop-loss triggers correctly and takes priority over time-based exit
    This addresses the critical issue found in the paper trading test
    """
    print("🚨 Testing Stop-Loss Priority Fix...")
    
    try:
        from risk_manager import check_risk_rules
        
        # Create test position with future unlock date (to avoid time-based exit confusion)
        future_unlock_date = (datetime.now() + timedelta(days=15)).isoformat() + 'Z'
        
        test_position = {
            'position_id': 1,
            'token_symbol': 'TEST',
            'entry_price_in_usdc': '2.50',  # Entry price
            'unlock_date': future_unlock_date,
            'amount_shorted': '1000'
        }
        
        print(f"📊 Test Position: {test_position['token_symbol']}")
        print(f"   Entry Price: ${test_position['entry_price_in_usdc']}")
        print(f"   Unlock Date: {test_position['unlock_date']}")
        print(f"   Stop-Loss Threshold: 15% (${Decimal('2.50') * Decimal('1.15'):.4f})")
        print(f"   Take-Profit Threshold: 10% (${Decimal('2.50') * Decimal('0.90'):.4f})")
        
        # Test scenarios in order of priority
        test_scenarios = [
            {
                'price': Decimal('2.88'),  # 15.2% above entry - should trigger stop-loss
                'expected_action': 'CLOSE',
                'expected_reason_contains': 'Stop-Loss triggered',
                'description': 'Stop-Loss Trigger Test (15.2% loss)'
            },
            {
                'price': Decimal('2.87'),  # Exactly at 14.8% - should NOT trigger stop-loss
                'expected_action': 'HOLD',
                'expected_reason_contains': 'Current P&L',
                'description': 'Just Below Stop-Loss (14.8% loss)'
            },
            {
                'price': Decimal('2.25'),  # 10% below entry - should trigger take-profit
                'expected_action': 'CLOSE',
                'expected_reason_contains': 'Take-Profit triggered',
                'description': 'Take-Profit Trigger Test (10% profit)'
            },
            {
                'price': Decimal('2.40'),  # 4% profit - should hold
                'expected_action': 'HOLD',
                'expected_reason_contains': 'Current P&L',
                'description': 'Normal Hold Position (4% profit)'
            }
        ]
        
        all_tests_passed = True
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🧪 Test {i}: {scenario['description']}")
            print(f"   Testing price: ${scenario['price']:.4f}")
            
            action, reason = check_risk_rules(test_position, scenario['price'])
            
            # Check if action matches expected
            action_correct = action == scenario['expected_action']
            reason_correct = scenario['expected_reason_contains'] in reason
            
            if action_correct and reason_correct:
                print(f"   ✅ PASS: {action} - {reason}")
            else:
                print(f"   ❌ FAIL: Expected {scenario['expected_action']} with '{scenario['expected_reason_contains']}'")
                print(f"        Got: {action} - {reason}")
                all_tests_passed = False
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Risk management test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_time_based_exit_priority():
    """
    Test that time-based exit only triggers when price-based rules don't apply
    """
    print("\n⏰ Testing Time-Based Exit Priority...")
    
    try:
        from risk_manager import check_risk_rules
        
        # Create test position with unlock date very close (should trigger time-based exit)
        near_unlock_date = (datetime.now() + timedelta(hours=12)).isoformat() + 'Z'  # 12 hours from now
        
        test_position = {
            'position_id': 2,
            'token_symbol': 'TIME_TEST',
            'entry_price_in_usdc': '2.50',
            'unlock_date': near_unlock_date,
            'amount_shorted': '1000'
        }
        
        print(f"📊 Test Position: {test_position['token_symbol']}")
        print(f"   Entry Price: ${test_position['entry_price_in_usdc']}")
        print(f"   Unlock Date: {test_position['unlock_date']} (very soon)")
        
        # Test scenarios where time-based exit should or shouldn't trigger
        test_scenarios = [
            {
                'price': Decimal('2.90'),  # Would trigger stop-loss - stop-loss should take priority
                'expected_action': 'CLOSE',
                'expected_reason_contains': 'Stop-Loss triggered',
                'description': 'Stop-Loss Priority Over Time-Based Exit'
            },
            {
                'price': Decimal('2.20'),  # Would trigger take-profit - take-profit should take priority
                'expected_action': 'CLOSE',
                'expected_reason_contains': 'Take-Profit triggered',
                'description': 'Take-Profit Priority Over Time-Based Exit'
            },
            {
                'price': Decimal('2.45'),  # Normal price - time-based exit should trigger
                'expected_action': 'CLOSE',
                'expected_reason_contains': 'Time-based exit triggered',
                'description': 'Time-Based Exit When No Price Rules Triggered'
            }
        ]
        
        all_tests_passed = True
        
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"\n🧪 Time Test {i}: {scenario['description']}")
            print(f"   Testing price: ${scenario['price']:.4f}")
            
            action, reason = check_risk_rules(test_position, scenario['price'])
            
            # Check if action matches expected
            action_correct = action == scenario['expected_action']
            reason_correct = scenario['expected_reason_contains'] in reason
            
            if action_correct and reason_correct:
                print(f"   ✅ PASS: {action} - {reason}")
            else:
                print(f"   ❌ FAIL: Expected {scenario['expected_action']} with '{scenario['expected_reason_contains']}'")
                print(f"        Got: {action} - {reason}")
                all_tests_passed = False
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Time-based exit test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all risk management tests"""
    print("🔧 Risk Management Logic Fix Validation")
    print("=" * 50)
    
    # Run tests
    stop_loss_test_passed = test_stop_loss_priority()
    time_based_test_passed = test_time_based_exit_priority()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Results Summary:")
    print(f"   Stop-Loss Priority Test: {'✅ PASS' if stop_loss_test_passed else '❌ FAIL'}")
    print(f"   Time-Based Exit Priority Test: {'✅ PASS' if time_based_test_passed else '❌ FAIL'}")
    
    if stop_loss_test_passed and time_based_test_passed:
        print("\n🎉 ALL TESTS PASSED! Risk management logic is working correctly.")
        print("✅ Stop-loss takes priority over time-based exits")
        print("✅ Take-profit takes priority over time-based exits")
        print("✅ Time-based exits only trigger when price rules don't apply")
        return True
    else:
        print("\n🚨 SOME TESTS FAILED! Risk management logic needs further fixes.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
