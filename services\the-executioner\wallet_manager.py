import os
from web3 import Web3
from eth_account import Account
import logging

# CRITICAL: Load secrets from environment variables provided by Render
INFURA_API_KEY = os.environ.get("INFURA_API_KEY")
WEB3_PROVIDER_URL = f"https://mainnet.infura.io/v3/{INFURA_API_KEY}"

# CRITICAL: Path to the private key file, mounted by Render Secret Files
PRIVATE_KEY_PATH = os.environ.get("PRIVATE_KEY_PATH", "/etc/secrets/trader-pk")

def get_wallet_and_provider():
    """
    Securely loads the private key from a file and connects to a web3 provider.
    NEVER hardcode private keys.
    """
    try:
        with open(PRIVATE_KEY_PATH, 'r') as f:
            private_key = f.read().strip()
    except FileNotFoundError:
        raise Exception(f"CRITICAL: Private key file not found at {PRIVATE_KEY_PATH}. Ensure Secret File is mounted.")

    if not private_key.startswith("0x"):
        private_key = "0x" + private_key

    if not INFURA_API_KEY:
        raise Exception("CRITICAL: INFURA_API_KEY environment variable not set")

    web3 = Web3(Web3.HTTPProvider(WEB3_PROVIDER_URL))
    if not web3.is_connected():
        raise ConnectionError("Failed to connect to web3 provider.")
        
    account = Account.from_key(private_key)
    
    # Log connection info (without sensitive data)
    logging.info(f"Connected to Ethereum mainnet via Infura")
    logging.info(f"Wallet address: {account.address}")
    logging.info(f"Current block: {web3.eth.block_number}")
    
    return web3, account

def get_eth_balance(web3, account):
    """Get ETH balance of the account"""
    try:
        balance_wei = web3.eth.get_balance(account.address)
        balance_eth = web3.from_wei(balance_wei, 'ether')
        return float(balance_eth)
    except Exception as e:
        logging.error(f"Error getting ETH balance: {e}")
        return 0.0

def get_token_balance(web3, account, token_address):
    """Get ERC20 token balance of the account"""
    try:
        # Standard ERC20 ABI for balanceOf function
        erc20_abi = [
            {
                "constant": True,
                "inputs": [{"name": "_owner", "type": "address"}],
                "name": "balanceOf",
                "outputs": [{"name": "balance", "type": "uint256"}],
                "type": "function"
            },
            {
                "constant": True,
                "inputs": [],
                "name": "decimals",
                "outputs": [{"name": "", "type": "uint8"}],
                "type": "function"
            }
        ]
        
        contract = web3.eth.contract(address=token_address, abi=erc20_abi)
        balance = contract.functions.balanceOf(account.address).call()
        decimals = contract.functions.decimals().call()
        
        # Convert to human readable format
        balance_formatted = balance / (10 ** decimals)
        return balance_formatted
        
    except Exception as e:
        logging.error(f"Error getting token balance for {token_address}: {e}")
        return 0.0

def estimate_gas_price(web3):
    """Get current gas price estimate"""
    try:
        gas_price = web3.eth.gas_price
        gas_price_gwei = web3.from_wei(gas_price, 'gwei')
        logging.info(f"Current gas price: {gas_price_gwei:.2f} Gwei")
        return gas_price
    except Exception as e:
        logging.error(f"Error estimating gas price: {e}")
        return web3.to_wei(20, 'gwei')  # Fallback to 20 Gwei
